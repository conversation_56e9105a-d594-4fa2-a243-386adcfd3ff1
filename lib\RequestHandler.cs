﻿using System;
using System.IO;
using System.Security.Cryptography.X509Certificates;
using Asn1;
using AuthTool.lib.Interop;
using AuthTool.Asn1;
namespace AuthTool {
    public class RubenException : Exception
    {
        public RubenException(string message)
            : base(message)
        {
        }
    }
    public class AuthProtoErrorException : RubenException
    {
        public AUTH_ERROR krbError;
        public AuthProtoErrorException(string message, AUTH_ERROR krbError)
            : base(message)
        {
            this.krbError = krbError;
        }
    }
    public class Ask
    {
        public static byte[] TKN(string userName, string domain, string keyString, Interop.AUTH_ETYPE etype, string outfile, bool ptt, string domainController = "", LUID luid = new LUID(), bool describe = false, bool opsec = false)
        {
            bool preauth = false;
            if (opsec)
                preauth = NoPreAuthTKN(userName, domain, keyString, etype, domainController, outfile, ptt, luid, describe, true);
            try
            {
                if (!preauth)
                {
                    Console.WriteLine("[*] Using {0} hash: {1}", etype, keyString);
                    Console.WriteLine("[*] Building AS-REQ (w/ preauth) for: '{0}\\{1}'", domain, userName);
                    AUTH_REQ userHashASREQ = AUTH_REQ.NewASReq(userName, domain, keyString, etype, opsec);
                    return InnerTKN(userHashASREQ, etype, outfile, ptt, domainController, luid, describe, true, opsec);
                }
            }
            catch (AuthProtoErrorException ex)
            {
                AUTH_ERROR error = ex.krbError;
                Console.WriteLine("\r\n[X] KRB-ERROR ({0}) : {1}\r\n", error.error_code, (Interop.AUTHPROTO_ERROR)error.error_code);
            }
            catch (RubenException ex)
            {
                Console.WriteLine("\r\n" + ex.Message + "\r\n");
            }
            return null;
        }
        public static bool NoPreAuthTKN(string userName, string domain, string keyString, Interop.AUTH_ETYPE etype, string domainController, string outfile, bool ptt, LUID luid = new LUID(), bool describe = false, bool verbose = false)
        {
            string dcIP = Networking.GetDCIP(domainController, true, domain);
            if (String.IsNullOrEmpty(dcIP)) { return false; }
            AUTH_REQ NoPreAuthASREQ = AUTH_REQ.NewASReq(userName, domain, etype, true);
            byte[] reqBytes = NoPreAuthASREQ.Encode().Encode();
            byte[] response = Networking.SendBytes(dcIP, 88, reqBytes);
            if (response == null)
            {
                return false;
            }
            AsnElt responseAsn = AsnElt.Decode(response, false);
            int responseTag = responseAsn.TagValue;
            if (responseTag == (int)Interop.AUTH_MESSAGE_TYPE.AUTH_REP)
            {
                Console.WriteLine("[-] AS-REQ w/o preauth successful! {0} has pre-authentication disabled!", userName);
                byte[] authBytes = HandleASREP(responseAsn, etype, keyString, outfile, ptt, luid, describe, verbose);
                return true;
            }
            return false;
        }
        public static X509Certificate2 FindCertificate(string certificate, string storePassword) {
            if (File.Exists(certificate)) {
                return new X509Certificate2(certificate, storePassword);
            } else {
                X509Store store = new X509Store(StoreName.My, StoreLocation.CurrentUser);
                store.Open(OpenFlags.ReadOnly);
                X509Certificate2 result = null;
                foreach (var cert in store.Certificates) {
                    if (string.Equals(certificate, cert.Subject, StringComparison.InvariantCultureIgnoreCase)) {
                        result = cert;
                        break;
                    } else if (string.Equals(certificate, cert.Thumbprint, StringComparison.InvariantCultureIgnoreCase)) {
                        result = cert;
                        break;
                    }
                }
                if (result != null && !String.IsNullOrEmpty(storePassword)) {
                    result.SetPinForPrivateKey(storePassword);
                }
                return result;
            }
        }
        public static byte[] TKN(string userName, string domain, string certFile, string certPass, Interop.AUTH_ETYPE etype, string outfile, bool ptt, string domainController = "", LUID luid = new LUID(), bool describe = false, bool verifyCerts = false) {
            try {
                X509Certificate2 cert;
                if (Helpers.IsBase64String(certFile))
                {
                    cert = new X509Certificate2(Convert.FromBase64String(certFile), certPass);
                }
                else
                {
                    cert = FindCertificate(certFile, certPass);
                }
                if(cert == null) {
                    Console.WriteLine("[!] Failed to find certificate for {0}", certFile);
                    return null;
                }
                KDCKeyAgreement agreement = new KDCKeyAgreement();
                Console.WriteLine("[*] Using PKINIT with etype {0} and subject: {1} ", etype, cert.Subject);
                Console.WriteLine("[*] Building AS-REQ (w/ PKINIT preauth) for: '{0}\\{1}'", domain, userName);
                AUTH_REQ pkinitASREQ = AUTH_REQ.NewASReq(userName, domain, cert, agreement, etype, verifyCerts);
                return InnerTKN(pkinitASREQ, etype, outfile, ptt, domainController, luid, describe, true);
            } catch (AuthProtoErrorException ex) {
                AUTH_ERROR error = ex.krbError;
                Console.WriteLine("\r\n[X] KRB-ERROR ({0}) : {1}\r\n", error.error_code, (Interop.AUTHPROTO_ERROR)error.error_code);
            } catch (RubenException ex) {
                Console.WriteLine("\r\n" + ex.Message + "\r\n");
            }
            return null;
        }
        public static bool GetPKInitRequest(AUTH_REQ asReq, out PA_PK_AUTH_REQ pkAsReq) {
            if (asReq.padata != null) {
                foreach (PA_DATA paData in asReq.padata) {
                    if (paData.type == Interop.PADATA_TYPE.PK_AUTH_REQ) {
                        pkAsReq = (PA_PK_AUTH_REQ)paData.value;
                        return true;
                    }
                }
            }
            pkAsReq = null;
            return false;
        }
        public static int GetKeySize(Interop.AUTH_ETYPE etype) {
            switch (etype) {
                 case Interop.AUTH_ETYPE.des_cbc_md5:
                    return 7;
                case Interop.AUTH_ETYPE.rc4_hmac:
                    return 16;
                case Interop.AUTH_ETYPE.aes128_cts_hmac_sha1:
                    return 16;
                case Interop.AUTH_ETYPE.aes256_cts_hmac_sha1:
                    return 32;
                default:
                    throw new ArgumentException("Only /des, /rc4, /aes128, and /aes256 are supported at this time");
            }
        }
        public static byte[] InnerTKN(AUTH_REQ asReq, Interop.AUTH_ETYPE etype, string outfile, bool ptt, string domainController = "", LUID luid = new LUID(), bool describe = false, bool verbose = false, bool opsec = false)
        {
            if ((ulong)luid != 0) {
                Console.WriteLine("[*] Target LUID : {0}", (ulong)luid);
            }
            string dcIP = Networking.GetDCIP(domainController, false);
            if (String.IsNullOrEmpty(dcIP))
            {
                throw new RubenException("[X] Unable to get domain controller address");
            }
            byte[] response = Networking.SendBytes(dcIP, 88, asReq.Encode().Encode());
            if (response == null)
            {
                throw new RubenException("[X] No answer from domain controller");
            }
            AsnElt responseAsn;
            try
            {
                responseAsn = AsnElt.Decode(response, false);
            }
            catch(Exception e)
            {
               throw new Exception($"Error parsing response AS-REQ: {e}.  Base64 response: {Convert.ToBase64String(response)}");
            }
            int responseTag = responseAsn.TagValue;
            if (responseTag == (int)Interop.AUTH_MESSAGE_TYPE.AUTH_REP)
            {
                if (verbose)
                {
                    Console.WriteLine("[+] TKN request successful!");
                }
                byte[] authBytes = HandleASREP(responseAsn, etype, asReq.keyString, outfile, ptt, luid, describe, verbose, asReq);
                return authBytes;
            }
            else if (responseTag == (int)Interop.AUTH_MESSAGE_TYPE.ERROR)
            {
                AUTH_ERROR error = new AUTH_ERROR(responseAsn.Sub[0]);
                throw new AuthProtoErrorException("", error);
            }
            else
            {
                throw new RubenException("[X] Unknown application tag: " + responseTag);
            }
        }
        public static void SVC(AUTH_CRED auth, string service, Interop.AUTH_ETYPE requestEType = Interop.AUTH_ETYPE.subkey_keymaterial, string outfile = "", bool ptt = false, string domainController = "", bool display = true, bool enterprise = false, bool roast = false, bool opsec = false, AUTH_CRED svc = null, bool usesvcdomain = false)
        {
            string userName = auth.enc_part.token_info[0].pname.name_string[0];
            string domain = auth.enc_part.token_info[0].prealm;
            Token token = auth.tokens[0];
            byte[] clientKey = auth.enc_part.token_info[0].key.keyvalue;
            Interop.AUTH_ETYPE paEType = (Interop.AUTH_ETYPE)auth.enc_part.token_info[0].key.keytype;
            string[] services = service.Split(',');
            foreach (string sname in services)
            {
                SVC(userName, domain, token, clientKey, paEType, sname, requestEType, outfile, ptt, domainController, display, enterprise, roast, opsec, svc, usesvcdomain);
                Console.WriteLine();
            }
        }
        public static byte[] SVC(string userName, string domain, Token providedToken, byte[] clientKey, Interop.AUTH_ETYPE paEType, string service, Interop.AUTH_ETYPE requestEType = Interop.AUTH_ETYPE.subkey_keymaterial, string outfile = "", bool ptt = false, string domainController = "", bool display = true, bool enterprise = false, bool roast = false, bool opsec = false, AUTH_CRED svc = null, bool usesvcdomain = false)
        {
            string dcIP = Networking.GetDCIP(domainController, display);
            if (String.IsNullOrEmpty(dcIP)) { return null; }
            if (display)
            {
                if (requestEType == Interop.AUTH_ETYPE.subkey_keymaterial)
                {
                    Console.WriteLine("[*] Requesting default etypes (RC4_HMAC, AES[128/256]_CTS_HMAC_SHA1) for the service token", requestEType);
                }
                else
                {
                    Console.WriteLine("[*] Requesting '{0}' etype for the service token", requestEType);
                }
                Console.WriteLine("[*] Building SVC-REQ request for: '{0}'", service);
            }
            byte[] svcBytes = SVC_REQ.NewSVCReq(userName, domain, service, providedToken, clientKey, paEType, requestEType, false, "", enterprise, roast, opsec, false, svc, usesvcdomain);
            byte[] response = Networking.SendBytes(dcIP, 88, svcBytes);
            if (response == null)
            {
                return null;
            }
            AsnElt responseAsn = AsnElt.Decode(response, false);
            int responseTag = responseAsn.TagValue;
            if (responseTag == (int)Interop.AUTH_MESSAGE_TYPE.SVC_REP)
            {
                if (display)
                {
                    Console.WriteLine("[+] SVC request successful!");
                }
                SVC_REP rep = new SVC_REP(responseAsn);
                byte[] outBytes = Crypto.AuthProtoDecrypt(paEType, Interop.AUTH_KEY_USAGE_SVC_REP_EP_SESSION_KEY, clientKey, rep.enc_part.cipher);
                AsnElt ae = AsnElt.Decode(outBytes, false);
                EncKDCRepPart encRepPart = new EncKDCRepPart(ae.Sub[0]);
                if (opsec && (!roast) && ((encRepPart.flags & Interop.TokenFlags.ok_as_delegate) != 0))
                {
                    byte[] tknBytes = SVC_REQ.NewSVCReq(userName, domain, string.Format("authsvc/{0}", domain), providedToken, clientKey, paEType, requestEType, false, "", enterprise, roast, opsec, true);
                    byte[] tknResponse = Networking.SendBytes(dcIP, 88, tknBytes);
                }
                AUTH_CRED cred = new AUTH_CRED();
                cred.tokens.Add(rep.token);
                KrbCredInfo info = new KrbCredInfo();
                info.key.keytype = encRepPart.key.keytype;
                info.key.keyvalue = encRepPart.key.keyvalue;
                info.prealm = rep.crealm;
                info.pname.name_type = rep.cname.name_type;
                info.pname.name_string = rep.cname.name_string;
                info.flags = encRepPart.flags;
                info.starttime = encRepPart.starttime;
                info.endtime = encRepPart.endtime;
                info.renew_till = encRepPart.renew_till;
                info.srealm = encRepPart.realm;
                info.sname.name_type = encRepPart.sname.name_type;
                info.sname.name_string = encRepPart.sname.name_string;
                cred.enc_part.token_info.Add(info);
                byte[] authBytes = cred.Encode().Encode();
                string authString = Convert.ToBase64String(authBytes);
                if (ptt)
                {
                    LSA.ImportToken(authBytes, new LUID());
                }
                if (display)
                {
                    Console.WriteLine("[*] base64(token.auth):\r\n", authString);
                    if (AuthTool.Program.wrapTokens)
                    {
                        foreach (string line in Helpers.Split(authString, 80))
                        {
                            Console.WriteLine("      {0}", line);
                        }
                    }
                    else
                    {
                        Console.WriteLine("      {0}", authString);
                    }
                    AUTH_CRED auth = new AUTH_CRED(authBytes);
                    LSA.DisplayToken(auth);
                }
                if (!String.IsNullOrEmpty(outfile))
                {
                    outfile = Helpers.MakeValidFileName(outfile);
                    if (Helpers.WriteBytesToFile(outfile, authBytes))
                    {
                        if (display)
                        {
                            Console.WriteLine("\r\n[*] Token written to {0}\r\n", outfile);
                        }
                    }
                }
                return authBytes;
            }
            else if (responseTag == (int)Interop.AUTH_MESSAGE_TYPE.ERROR)
            {
                AUTH_ERROR error = new AUTH_ERROR(responseAsn.Sub[0]);
                Console.WriteLine("\r\n[X] KRB-ERROR ({0}) : {1}\r\n", error.error_code, (Interop.AUTHPROTO_ERROR)error.error_code);
            }
            else
            {
                Console.WriteLine("\r\n[X] Unknown application tag: {0}", responseTag);
            }
            return null;
        }
        private static byte[] HandleASREP(AsnElt responseAsn, Interop.AUTH_ETYPE etype, string keyString, string outfile, bool ptt, LUID luid = new LUID(), bool describe = false, bool verbose = false, AUTH_REQ asReq = null)
        {
            AUTH_REP rep = new AUTH_REP(responseAsn);
            byte[] key;
            if (GetPKInitRequest(asReq, out PA_PK_AUTH_REQ pkAsReq)) {
                PA_PK_AUTH_REP pkAsRep = (PA_PK_AUTH_REP)rep.padata[0].value;
                key = pkAsReq.Agreement.GenerateKey(pkAsRep.DHRepInfo.KDCDHKeyInfo.SubjectPublicKey.DepadLeft(), new byte[0],
                    pkAsRep.DHRepInfo.ServerDHNonce, GetKeySize(etype));
            } else {
                key = Helpers.StringToByteArray(asReq.keyString);
            }
            byte[] outBytes;
            if (etype == Interop.AUTH_ETYPE.des_cbc_md5)
            {
                outBytes = Crypto.AuthProtoDecrypt(etype, Interop.AUTH_KEY_USAGE_SVC_REP_EP_SESSION_KEY, key, rep.enc_part.cipher);
            }
            else if (etype == Interop.AUTH_ETYPE.rc4_hmac)
            {
                outBytes = Crypto.AuthProtoDecrypt(etype, Interop.AUTH_KEY_USAGE_SVC_REP_EP_SESSION_KEY, key, rep.enc_part.cipher);
            }
            else if (etype == Interop.AUTH_ETYPE.aes128_cts_hmac_sha1)
            {
                outBytes = Crypto.AuthProtoDecrypt(etype, Interop.AUTH_KEY_USAGE_AUTH_REP_EP_SESSION_KEY, key, rep.enc_part.cipher);
            }
            else if (etype == Interop.AUTH_ETYPE.aes256_cts_hmac_sha1)
            {
                outBytes = Crypto.AuthProtoDecrypt(etype, Interop.AUTH_KEY_USAGE_AUTH_REP_EP_SESSION_KEY, key, rep.enc_part.cipher);
            }
            else
            {
                throw new RubenException("[X] Encryption type \"" + etype + "\" not currently supported");
            }
            AsnElt ae = AsnElt.Decode(outBytes, false);
            EncKDCRepPart encRepPart = new EncKDCRepPart(ae.Sub[0]);
            AUTH_CRED cred = new AUTH_CRED();
            cred.tokens.Add(rep.token);
            KrbCredInfo info = new KrbCredInfo();
            info.key.keytype = encRepPart.key.keytype;
            info.key.keyvalue = encRepPart.key.keyvalue;
            info.prealm = encRepPart.realm;
            info.pname.name_type = rep.cname.name_type;
            info.pname.name_string = rep.cname.name_string;
            info.flags = encRepPart.flags;
            info.starttime = encRepPart.starttime;
            info.endtime = encRepPart.endtime;
            info.renew_till = encRepPart.renew_till;
            info.srealm = encRepPart.realm;
            info.sname.name_type = encRepPart.sname.name_type;
            info.sname.name_string = encRepPart.sname.name_string;
            cred.enc_part.token_info.Add(info);
            byte[] authBytes = cred.Encode().Encode();
            if (verbose)
            {
                string authString = Convert.ToBase64String(authBytes);
                Console.WriteLine("[*] base64(token.auth):\r\n", authString);
                if (AuthTool.Program.wrapTokens)
                {
                    foreach (string line in Helpers.Split(authString, 80))
                    {
                        Console.WriteLine("      {0}", line);
                    }
                }
                else
                {
                    Console.WriteLine("      {0}", authString);
                }
            }
            if (!String.IsNullOrEmpty(outfile))
            {
                outfile = Helpers.MakeValidFileName(outfile);
                if (Helpers.WriteBytesToFile(outfile, authBytes))
                {
                    if (verbose)
                    {
                        Console.WriteLine("\r\n[*] Token written to {0}\r\n", outfile);
                    }
                }
            }
            if (ptt || ((ulong)luid != 0))
            {
                LSA.ImportToken(authBytes, luid);
            }
            if (describe)
            {
                AUTH_CRED auth = new AUTH_CRED(authBytes);
                LSA.DisplayToken(auth);
            }
            return authBytes;
        }
    }
}