﻿using System;
namespace AuthTool.Domain
{
    public static class AppInfo
    {
        public static void ShowLogo()
        {
            Console.WriteLine("");
            Console.WriteLine("  Authentication Tool v2.0.0");
            Console.WriteLine("  Developed for enterprise testing");
            Console.WriteLine("");
        }
        public static void ShowUsage()
        {
            string usage = @"
 Authentication Tool Usage:

 Commands:
    request      - Request authentication tokens
    renew        - Renew existing tokens
    extract      - Extract token information
    inject       - Inject tokens into session
    enumerate    - Enumerate available resources
    brute        - Brute force attack
    monitor      - Monitor for new tokens
    harvest      - Harvest available tokens
    describe     - Describe token contents
    purge        - Purge current tokens
    hash         - Calculate hashes
    currentid    - Display current identifier

 Options:
    /user:USER             - Target user
    /password:PASS         - User password
    /domain:DOMAIN         - Target domain
    /dc:DOMAIN_CONTROLLER  - Domain controller
    /outfile:FILENAME      - Output file
    /nowrap                - Disable output wrapping

 Example:
    authtool.exe request /user:admin /password:pass123

";
            Console.WriteLine(usage);
        }
    }
}