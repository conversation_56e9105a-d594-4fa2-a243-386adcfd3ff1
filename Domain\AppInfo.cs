using System;
using AuthTool.lib;

namespace AuthTool.Domain
{
    public static class AppInfo
    {
        public static void ShowLogo()
        {
            // Generic authentication tool banner
            Console.WriteLine(StringObfuscator.GetString("Cg=="));  // \r\n
            Console.WriteLine(StringObfuscator.GetString("ICBBdXRoZW50aWNhdGlvbiBUb29sIHYyLjAuMA=="));  // Authentication Tool v2.0.0
            Console.WriteLine(StringObfuscator.GetString("ICBEZXZlbG9wZWQgZm9yIGVudGVycHJpc2UgdGVzdGluZw=="));  // Developed for enterprise testing
            Console.WriteLine(StringObfuscator.GetString("Cg=="));  // \r\n
        }

        public static void ShowUsage()
        {
            // Obfuscated usage text - much shorter and generic
            string usage = StringObfuscator.GetString("CiBBdXRoZW50aWNhdGlvbiBUb29sIFVzYWdlOgoKIENvbW1hbmRzOgogICAgcmVxdWVzdCAgICAgIC0gUmVxdWVzdCBhdXRoZW50aWNhdGlvbiB0b2tlbnMKICAgIHJlbmV3ICAgICAgIC0gUmVuZXcgZXhpc3RpbmcgdG9rZW5zCiAgICBleHRyYWN0ICAgICAtIEV4dHJhY3QgdG9rZW4gaW5mb3JtYXRpb24KICAgIGluamVjdCAgICAgIC0gSW5qZWN0IHRva2VucyBpbnRvIHNlc3Npb24KICAgIGVudW1lcmF0ZSAgIC0gRW51bWVyYXRlIGF2YWlsYWJsZSByZXNvdXJjZXMKICAgIGJydXRlICAgICAgIC0gQnJ1dGUgZm9yY2UgYXR0YWNrCiAgICBtb25pdG9yICAgICAtIE1vbml0b3IgZm9yIG5ldyB0b2tlbnMKICAgIGhhcnZlc3QgICAgIC0gSGFydmVzdCBhdmFpbGFibGUgdG9rZW5zCiAgICBkZXNjcmliZSAgICAtIERlc2NyaWJlIHRva2VuIGNvbnRlbnRzCiAgICBwdXJnZSAgICAgICAtIFB1cmdlIGN1cnJlbnQgdG9rZW5zCiAgICBoYXNoICAgICAgICAtIENhbGN1bGF0ZSBoYXNoZXMKICAgIGN1cnJlbnRpZCAgIC0gRGlzcGxheSBjdXJyZW50IGlkZW50aWZpZXIKCiBPcHRpb25zOgogICAgL3VzZXI6VVNFUiAgICAgICAgICAgIC0gVGFyZ2V0IHVzZXIKICAgIC9wYXNzd29yZDpQQVNTICAgICAgICAtIFVzZXIgcGFzc3dvcmQKICAgIC9kb21haW46RE9NQUlOICAgICAgICAtIFRhcmdldCBkb21haW4KICAgIC9kYzpET01BSU5fQ09OVFJPTExFUiAtIERvbWFpbiBjb250cm9sbGVyCiAgICAvb3V0ZmlsZTpGSUxFTkFNRSAgICAgLSBPdXRwdXQgZmlsZQogICAgL25vd3JhcCAgICAgICAgICAgICAgIC0gRGlzYWJsZSBvdXRwdXQgd3JhcHBpbmcKCiBFeGFtcGxlOgogICAgYXV0aHRvb2wuZXhlIHJlcXVlc3QgL3VzZXI6YWRtaW4gL3Bhc3N3b3JkOnBhc3MxMjMKCg==");
            Console.WriteLine(usage);
        }
    }
}
