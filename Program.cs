﻿using AuthTool.Domain;
using AuthTool.lib;
using System;
using System.Collections.Generic;
using System.IO;
namespace AuthTool
{
    public class Program
    {
        public static bool wrapTokens = true;
        private static void FileExecute(string commandName, Dictionary<string, string> parsedArgs)
        {
            string file = parsedArgs["/consoleoutfile"];
            TextWriter realStdOut = Console.Out;
            TextWriter realStdErr = Console.Error;
            using (StreamWriter writer = new StreamWriter(file, true))
            {
                writer.AutoFlush = true;
                Console.SetOut(writer);
                Console.SetError(writer);
                MainExecute(commandName, parsedArgs);
                Console.Out.Flush();
                Console.Error.Flush();
            }
            Console.SetOut(realStdOut);
            Console.SetError(realStdErr);
        }
        private static void MainExecute(string commandName, Dictionary<string,string> parsedArgs)
        {
            AppInfo.ShowLogo();
            try
            {
                var commandFound = new OperationRegistry().ExecuteOperation(commandName, parsedArgs);
                if (commandFound == false)
                    AppInfo.ShowUsage();
            }
            catch (Exception e)
            {
                Console.WriteLine(StringObfuscator.GetString("DQoKWyFdIFVuaGFuZGxlZCBhcHBsaWNhdGlvbiBleGNlcHRpb246DQoK"));
                Console.WriteLine(e);
            }
        }
        public static string MainString(string command)
        {
            string[] args = command.Split();
            var parsed = ParameterParser.Parse(args);
            if (parsed.ParsedOk == false)
            {
                AppInfo.ShowLogo();
                AppInfo.ShowUsage();
                return "Error parsing arguments: ${command}";
            }
            var commandName = args.Length != 0 ? args[0] : "";
            TextWriter realStdOut = Console.Out;
            TextWriter realStdErr = Console.Error;
            TextWriter stdOutWriter = new StringWriter();
            TextWriter stdErrWriter = new StringWriter();
            Console.SetOut(stdOutWriter);
            Console.SetError(stdErrWriter);
            MainExecute(commandName, parsed.Arguments);
            Console.Out.Flush();
            Console.Error.Flush();
            Console.SetOut(realStdOut);
            Console.SetError(realStdErr);
            string output = "";
            output += stdOutWriter.ToString();
            output += stdErrWriter.ToString();
            return output;
        }
        public static void Main(string[] args)
        {
            var parsed = ParameterParser.Parse(args);
            if (parsed.ParsedOk == false) {
                AppInfo.ShowLogo();
                AppInfo.ShowUsage();
                return;
            }
            var commandName = args.Length != 0 ? args[0] : "";
            if (parsed.Arguments.ContainsKey("/nowrap"))
            {
                wrapTokens = false;
            }
            if (parsed.Arguments.ContainsKey("/consoleoutfile")) {
                FileExecute(commandName, parsed.Arguments);
            }
            else
            {
                MainExecute(commandName, parsed.Arguments);
            }
        }
    }
}