﻿using System;
using Asn1;
using System.Collections.Generic;
using System.Text;
namespace AuthTool
{
    public class AUTH_ERROR
    {
        public AUTH_ERROR(byte[] errorBytes)
        {
        }
        public AUTH_ERROR(AsnElt body)
        {
            foreach (AsnElt s in body.Sub)
            {
                switch (s.TagValue)
                {
                    case 0:
                        pvno = Convert.ToUInt32(s.Sub[0].GetInteger());
                        break;
                    case 1:
                        msg_type = Convert.ToUInt32(s.Sub[0].GetInteger());
                        break;
                    case 2:
                        ctime = s.Sub[0].GetTime();
                        break;
                    case 3:
                        cusec = Convert.ToUInt32(s.Sub[0].GetInteger());
                        break;
                    case 4:
                        stime = s.Sub[0].GetTime();
                        break;
                    case 5:
                        susec = Convert.ToUInt32(s.Sub[0].GetInteger());
                        break;
                    case 6:
                        error_code = Convert.ToUInt32(s.Sub[0].GetInteger());
                        break;
                    case 7:
                        crealm = Encoding.ASCII.GetString(s.Sub[0].GetOctetString());
                        break;
                    case 8:
                        cname = new PrincipalName(s.Sub[0]);
                        break;
                    case 9:
                        realm = Encoding.ASCII.GetString(s.Sub[0].GetOctetString());
                        break;
                    case 10:
                        sname = new PrincipalName(s.Sub[0]);
                        break;
                    default:
                        break;
                }
            }
        }
        public long pvno { get; set; }
        public long msg_type { get; set; }
        public DateTime ctime { get; set; }
        public long cusec { get; set; }
        public DateTime stime { get; set; }
        public long susec { get; set; }
        public long error_code { get; set; }
        public string crealm { get; set; }
        public PrincipalName cname { get; set; }
        public string realm { get; set; }
        public PrincipalName sname { get; set; }
        public List<Token> tokens { get; set; }
        public EncKrbCredPart enc_part { get; set; }
    }
}