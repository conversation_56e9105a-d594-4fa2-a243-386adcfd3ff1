using static AuthTool.Interop;
﻿using Asn1;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
namespace AuthTool
{
    public class SVC_REQ
    {
        public static byte[] NewTGSReq(string userName, string domain, string sname, Token providedToken, byte[] clientKey, Interop.AUTH_ETYPE paEType, Interop.AUTH_ETYPE requestEType = Interop.AUTH_ETYPE.subkey_keymaterial, bool renew = false, string s4uUser = "", bool enterprise = false, bool roast = false, bool opsec = false, bool unconstrained = false, AUTH_CRED svc = null, bool usesvcdomain = false)
        {
            SVC_REQ req = new SVC_REQ(!opsec);
            if (!opsec)
            {
                req.req_body.cname.name_string.Add(userName);
            }
            string targetDomain = "";
            string[] parts = sname.Split('/');
            if (!(roast) && (parts.Length > 1) && (parts[0] != "authsvc") && ((svc == null) || usesvcdomain))
            {
                targetDomain = parts[1].Substring(parts[1].IndexOf('.')+1);
                string[] targetParts = targetDomain.Split(':');
                if (targetParts.Length > 1)
                {
                    targetDomain = targetParts[0];
                }
            }
            else if (enterprise)
            {
                targetDomain = sname.Split('@')[1];
            }
            else
            {
                targetDomain = domain;
            }
            req.req_body.realm = targetDomain.ToUpper();
            if (requestEType == Interop.AUTH_ETYPE.subkey_keymaterial)
            {
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.aes256_cts_hmac_sha1);
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.aes128_cts_hmac_sha1);
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.rc4_hmac);
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.rc4_hmac_exp);
            }
            else if ((opsec) && (parts.Length > 1) && (parts[0] != "authsvc"))
            {
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.aes256_cts_hmac_sha1);
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.aes128_cts_hmac_sha1);
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.rc4_hmac);
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.rc4_hmac_exp);
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.old_exp);
            }
            else
            {
                req.req_body.etypes.Add(requestEType);
            }
            if (!String.IsNullOrEmpty(s4uUser))
            {
                req.req_body.sname.name_type = Interop.PRINCIPAL_TYPE.NT_PRINCIPAL;
                req.req_body.sname.name_string.Add(userName);
                if (!opsec)
                    req.req_body.kdcOptions = req.req_body.kdcOptions | Interop.KdcOptions.ENCTKTINSKEY;
                if (opsec)
                    req.req_body.etypes.Add(Interop.AUTH_ETYPE.old_exp);
            }
            else
            {
                if (enterprise)
                {
                    req.req_body.sname.name_type = Interop.PRINCIPAL_TYPE.NT_ENTERPRISE;
                    req.req_body.sname.name_string.Add(sname);
                    req.req_body.kdcOptions = req.req_body.kdcOptions | Interop.KdcOptions.CANONICALIZE;
                }
                else if (parts.Length == 1)
                {
                    req.req_body.sname.name_type = Interop.PRINCIPAL_TYPE.NT_SRV_INST;
                    req.req_body.sname.name_string.Add(sname);
                    req.req_body.sname.name_string.Add(domain);
                }
                else if (parts.Length == 2)
                {
                    req.req_body.sname.name_type = Interop.PRINCIPAL_TYPE.NT_SRV_INST;
                    req.req_body.sname.name_string.Add(parts[0]);
                    req.req_body.sname.name_string.Add(parts[1]);
                }
                else if (parts.Length == 3)
                {
                    req.req_body.sname.name_type = Interop.PRINCIPAL_TYPE.NT_SRV_HST;
                    req.req_body.sname.name_string.Add(parts[0]);
                    req.req_body.sname.name_string.Add(parts[1]);
                    req.req_body.sname.name_string.Add(parts[2]);
                }
                else
                {
                    Console.WriteLine("[X] Error: invalid SVC_REQ sname '{0}'", sname);
                }
            }
            if (renew)
            {
                req.req_body.kdcOptions = req.req_body.kdcOptions | Interop.KdcOptions.RENEW;
            }
            if (svc!=null)
            {
                req.req_body.additional_tokens.Add(svc.tokens[0]);
                req.req_body.kdcOptions = req.req_body.kdcOptions | Interop.KdcOptions.CONSTRAINED_DELEGATION | Interop.KdcOptions.CANONICALIZE;
                req.req_body.kdcOptions = req.req_body.kdcOptions & ~Interop.KdcOptions.RENEWABLEOK;
                PA_DATA pac_options = new PA_DATA(false, false, false, true);
                req.padata.Add(pac_options);
            }
            byte[] cksum_Bytes = null;
            if (opsec)
            {
                req.req_body.kdcOptions = req.req_body.kdcOptions | Interop.KdcOptions.CANONICALIZE;
                if (!unconstrained)
                    req.req_body.kdcOptions = req.req_body.kdcOptions & ~Interop.KdcOptions.RENEWABLEOK;
                if (unconstrained)
                    req.req_body.kdcOptions = req.req_body.kdcOptions | Interop.KdcOptions.FORWARDED;
                string hostName = Dns.GetHostName().ToUpper();
                string targetHostName;
                if (parts.Length > 1)
                {
                    targetHostName = parts[1].Substring(0, parts[1].IndexOf('.')).ToUpper();
                }
                else
                {
                    targetHostName = hostName;
                }
                if ((hostName != targetHostName) && String.IsNullOrEmpty(s4uUser) && (!unconstrained))
                {
                    List<AuthorizationData> tmp = new List<AuthorizationData>();
                    AuthorizationData restrictions = new AuthorizationData(Interop.AuthorizationDataType.AUTH_AUTH_DATA_TOKEN_RESTRICTIONS);
                    AuthorizationData kerbLocal = new AuthorizationData(Interop.AuthorizationDataType.AUTH_LOCAL);
                    tmp.Add(restrictions);
                    tmp.Add(kerbLocal);
                    AuthorizationData authorizationData = new AuthorizationData(tmp);
                    byte[] authorizationDataBytes = authorizationData.Encode().Encode();
                    byte[] enc_authorization_data = Crypto.KerberosEncrypt(requestEType, Interop.AUTH_KEY_USAGE_SVC_REQ_ENC_AUTHOIRZATION_DATA, clientKey, authorizationDataBytes);
                    req.req_body.enc_authorization_data = new EncryptedData((Int32)requestEType, enc_authorization_data);
                }
                if (!String.IsNullOrEmpty(s4uUser))
                {
                    DateTime till = DateTime.Now;
                    till = till.AddMinutes(15);
                    req.req_body.till = till;
                }
                AsnElt req_Body_ASN = req.req_body.Encode();
                AsnElt req_Body_ASNSeq = AsnElt.Make(AsnElt.SEQUENCE, new[] { req_Body_ASN });
                req_Body_ASNSeq = AsnElt.MakeImplicit(AsnElt.CONTEXT, 4, req_Body_ASNSeq);
                byte[] req_Body_Bytes = req_Body_ASNSeq.CopyValue();
                cksum_Bytes = Crypto.KerberosChecksum(clientKey, req_Body_Bytes, Interop.AUTH_CHECKSUM_ALGORITHM.AUTH_CHECKSUM_RSA_MD5);
            }
            PA_DATA padata = new PA_DATA(domain, userName, providedToken, clientKey, paEType, opsec, cksum_Bytes);
            req.padata.Add(padata);
            if (opsec && (!String.IsNullOrEmpty(s4uUser)))
            {
                domain = domain.ToLower();
            }
            if (!String.IsNullOrEmpty(s4uUser))
            {
                PA_DATA s4upadata = new PA_DATA(clientKey, s4uUser, domain);
                req.padata.Add(s4upadata);
            }
            else if (opsec)
            {
                PA_DATA padataoptions = new PA_DATA(false, true, false, false);
                req.padata.Add(padataoptions);
            }
            return req.Encode().Encode();
        }
        public static byte[] NewTGSReq(string userName, string domain, string targetDomain, Token providedToken, byte[] clientKey, Interop.AUTH_ETYPE paEType, Interop.AUTH_ETYPE requestEType)
        {
            SVC_REQ req = new SVC_REQ(cname: false);
            PA_DATA padata = new PA_DATA(domain, userName, providedToken, clientKey, paEType);
            req.padata.Add(padata);
            req.req_body.realm = domain;
            if (requestEType == Interop.AUTH_ETYPE.subkey_keymaterial)
            {
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.aes256_cts_hmac_sha1);
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.aes128_cts_hmac_sha1);
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.rc4_hmac);
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.rc4_hmac_exp);
            }
            else
            {
                req.req_body.etypes.Add(requestEType);
            }
            PA_DATA padataoptions = new PA_DATA(false, true, false, false);
            req.padata.Add(padataoptions);
            req.req_body.sname.name_type = Interop.PRINCIPAL_TYPE.NT_SRV_INST;
            req.req_body.sname.name_string.Add("authsvc");
            req.req_body.sname.name_string.Add(targetDomain);
            req.req_body.kdcOptions = req.req_body.kdcOptions | Interop.KdcOptions.CANONICALIZE | Interop.KdcOptions.FORWARDABLE;
            req.req_body.kdcOptions = req.req_body.kdcOptions & ~Interop.KdcOptions.RENEWABLEOK & ~Interop.KdcOptions.RENEW;
            return req.Encode().Encode();
        }
        public static byte[] NewTGSReq(string userName, string targetUser, Token providedToken, byte[] clientKey, Interop.AUTH_ETYPE paEType, Interop.AUTH_ETYPE requestEType, bool cross = true, string requestDomain = "")
        {
            SVC_REQ req = new SVC_REQ(cname: false);
            string domain = userName.Split('@')[1];
            string targetDomain = targetUser.Split('@')[1];
            PA_DATA padata = new PA_DATA(domain, userName.Split('@')[0], providedToken, clientKey, paEType);
            req.padata.Add(padata);
            if (cross)
            {
                if (String.IsNullOrEmpty(requestDomain))
                    requestDomain = targetDomain;
                req.req_body.realm = requestDomain;
            }
            else
            {
                req.req_body.realm = domain;
            }
            if (requestEType == Interop.AUTH_ETYPE.subkey_keymaterial)
            {
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.aes256_cts_hmac_sha1);
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.aes128_cts_hmac_sha1);
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.rc4_hmac);
                req.req_body.etypes.Add(Interop.AUTH_ETYPE.rc4_hmac_exp);
            }
            else
            {
                req.req_body.etypes.Add(requestEType);
            }
            PA_DATA s4upadata = new PA_DATA(clientKey, targetUser, targetDomain);
            req.padata.Add(s4upadata);
            req.req_body.sname.name_type = Interop.PRINCIPAL_TYPE.NT_ENTERPRISE;
            req.req_body.sname.name_string.Add(userName);
            req.req_body.kdcOptions = req.req_body.kdcOptions | Interop.KdcOptions.CANONICALIZE | Interop.KdcOptions.FORWARDABLE;
            req.req_body.kdcOptions = req.req_body.kdcOptions & ~Interop.KdcOptions.RENEWABLEOK & ~Interop.KdcOptions.RENEW;
            return req.Encode().Encode();
        }
        public static byte[] NewTGSReq(byte[] auth)
        {
            return null;
        }
        public SVC_REQ(bool cname = true)
        {
            pvno = 5;
            msg_type = (long)Interop.AUTH_MESSAGE_TYPE.SVC_REQ;
            padata = new List<PA_DATA>();
            req_body = new KDCReqBody(c: cname);
        }
        public AsnElt Encode()
        {
            AsnElt pvnoAsn = AsnElt.MakeInteger(pvno);
            AsnElt pvnoSeq = AsnElt.Make(AsnElt.SEQUENCE, new[] { pvnoAsn });
            pvnoSeq = AsnElt.MakeImplicit(AsnElt.CONTEXT, 1, pvnoSeq);
            AsnElt msg_type_ASN = AsnElt.MakeInteger(msg_type);
            AsnElt msg_type_ASNSeq = AsnElt.Make(AsnElt.SEQUENCE, new[] { msg_type_ASN });
            msg_type_ASNSeq = AsnElt.MakeImplicit(AsnElt.CONTEXT, 2, msg_type_ASNSeq);
            List<AsnElt> padatas = new List<AsnElt>();
            foreach (PA_DATA pa in padata)
            {
                padatas.Add(pa.Encode());
            }
            AsnElt padata_ASNSeq = AsnElt.Make(AsnElt.SEQUENCE, padatas.ToArray());
            AsnElt padata_ASNSeq2 = AsnElt.Make(AsnElt.SEQUENCE, new[] { padata_ASNSeq });
            padata_ASNSeq = AsnElt.MakeImplicit(AsnElt.CONTEXT, 3, padata_ASNSeq2);
            AsnElt req_Body_ASN = req_body.Encode();
            AsnElt req_Body_ASNSeq = AsnElt.Make(AsnElt.SEQUENCE, new[] { req_Body_ASN });
            req_Body_ASNSeq = AsnElt.MakeImplicit(AsnElt.CONTEXT, 4, req_Body_ASNSeq);
            AsnElt[] total = new[] { pvnoSeq, msg_type_ASNSeq, padata_ASNSeq, req_Body_ASNSeq };
            AsnElt seq = AsnElt.Make(AsnElt.SEQUENCE, total);
            AsnElt totalSeq = AsnElt.Make(AsnElt.SEQUENCE, new[] { seq });
            totalSeq = AsnElt.MakeImplicit(AsnElt.APPLICATION, 12, totalSeq);
            return totalSeq;
        }
        public long pvno { get; set; }
        public long msg_type { get; set; }
        public List<PA_DATA> padata { get; set; }
        public KDCReqBody req_body { get; set; }
    }
}