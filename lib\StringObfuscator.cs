using System;
using System.Text;

namespace AuthTool.lib
{
    public static class StringObfuscator
    {
        // XOR key for string obfuscation - change this to any value you prefer
        private static readonly byte XorKey = 0x42;
        
        // Simple character substitution map
        private static readonly char[] SubstitutionMap = {
            'a', 'z', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', 'q', 's', 'd', 'f', 'g', 'h',
            'j', 'k', 'l', 'm', 'w', 'x', 'c', 'v', 'b', 'n', 'A', 'Z', 'E', 'R', 'T', 'Y',
            'U', 'I', 'O', 'P', 'Q', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'W', 'X',
            'C', 'V', 'B', 'N', '0', '9', '8', '7', '6', '5', '4', '3', '2', '1', ' ', '!',
            '@', '#', '$', '%', '^', '&', '*', '(', ')', '-', '_', '=', '+', '[', ']', '{',
            '}', '|', '\\', ':', ';', '"', '\'', '<', '>', ',', '.', '?', '/', '~', '`'
        };

        /// <summary>
        /// Obfuscates a string using XOR encoding
        /// </summary>
        /// <param name="input">The string to obfuscate</param>
        /// <returns>Base64 encoded XOR obfuscated string</returns>
        public static string ObfuscateXor(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            byte[] obfuscatedBytes = new byte[inputBytes.Length];

            for (int i = 0; i < inputBytes.Length; i++)
            {
                obfuscatedBytes[i] = (byte)(inputBytes[i] ^ XorKey);
            }

            return Convert.ToBase64String(obfuscatedBytes);
        }

        /// <summary>
        /// Deobfuscates a XOR encoded string
        /// </summary>
        /// <param name="obfuscatedInput">Base64 encoded XOR obfuscated string</param>
        /// <returns>Original string</returns>
        public static string DeobfuscateXor(string obfuscatedInput)
        {
            if (string.IsNullOrEmpty(obfuscatedInput))
                return string.Empty;

            try
            {
                byte[] obfuscatedBytes = Convert.FromBase64String(obfuscatedInput);
                byte[] deobfuscatedBytes = new byte[obfuscatedBytes.Length];

                for (int i = 0; i < obfuscatedBytes.Length; i++)
                {
                    deobfuscatedBytes[i] = (byte)(obfuscatedBytes[i] ^ XorKey);
                }

                return Encoding.UTF8.GetString(deobfuscatedBytes);
            }
            catch
            {
                return obfuscatedInput; // Return original if deobfuscation fails
            }
        }

        /// <summary>
        /// Simple character substitution obfuscation
        /// </summary>
        /// <param name="input">The string to obfuscate</param>
        /// <returns>Character substituted string</returns>
        public static string ObfuscateSubstitution(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            StringBuilder result = new StringBuilder();
            Random rand = new Random(42); // Fixed seed for consistent results

            foreach (char c in input)
            {
                if (char.IsLetterOrDigit(c) || char.IsPunctuation(c) || char.IsWhiteSpace(c))
                {
                    int index = rand.Next(0, SubstitutionMap.Length);
                    result.Append(SubstitutionMap[index]);
                }
                else
                {
                    result.Append(c);
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// ROT13-style character rotation
        /// </summary>
        /// <param name="input">The string to obfuscate</param>
        /// <returns>ROT13 obfuscated string</returns>
        public static string ObfuscateRot13(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            StringBuilder result = new StringBuilder();

            foreach (char c in input)
            {
                if (char.IsLetter(c))
                {
                    char baseChar = char.IsUpper(c) ? 'A' : 'a';
                    result.Append((char)((c - baseChar + 13) % 26 + baseChar));
                }
                else
                {
                    result.Append(c);
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// Deobfuscates ROT13 encoded string
        /// </summary>
        /// <param name="obfuscatedInput">ROT13 obfuscated string</param>
        /// <returns>Original string</returns>
        public static string DeobfuscateRot13(string obfuscatedInput)
        {
            // ROT13 is its own inverse
            return ObfuscateRot13(obfuscatedInput);
        }

        /// <summary>
        /// Splits a string into chunks and reverses each chunk
        /// </summary>
        /// <param name="input">The string to obfuscate</param>
        /// <param name="chunkSize">Size of each chunk to reverse</param>
        /// <returns>Chunk-reversed string</returns>
        public static string ObfuscateChunkReverse(string input, int chunkSize = 4)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            StringBuilder result = new StringBuilder();
            
            for (int i = 0; i < input.Length; i += chunkSize)
            {
                int length = Math.Min(chunkSize, input.Length - i);
                string chunk = input.Substring(i, length);
                char[] chunkArray = chunk.ToCharArray();
                Array.Reverse(chunkArray);
                result.Append(new string(chunkArray));
            }

            return result.ToString();
        }

        /// <summary>
        /// Deobfuscates chunk-reversed string
        /// </summary>
        /// <param name="obfuscatedInput">Chunk-reversed string</param>
        /// <param name="chunkSize">Size of each chunk that was reversed</param>
        /// <returns>Original string</returns>
        public static string DeobfuscateChunkReverse(string obfuscatedInput, int chunkSize = 4)
        {
            // Chunk reverse is its own inverse
            return ObfuscateChunkReverse(obfuscatedInput, chunkSize);
        }

        /// <summary>
        /// Convenience method to get a deobfuscated string at runtime
        /// Uses XOR deobfuscation by default
        /// </summary>
        /// <param name="obfuscatedString">The obfuscated string</param>
        /// <returns>Deobfuscated string</returns>
        public static string GetString(string obfuscatedString)
        {
            return DeobfuscateXor(obfuscatedString);
        }

        /// <summary>
        /// Helper method to obfuscate strings during development
        /// Call this with your plain text to get the obfuscated version
        /// </summary>
        /// <param name="plainText">Plain text to obfuscate</param>
        /// <returns>Obfuscated string ready for use in code</returns>
        public static string PrepareString(string plainText)
        {
            return ObfuscateXor(plainText);
        }
    }
}
