using System;
using System.Text;
namespace AuthTool.lib
{
    public static class StringObfuscator
    {
        private static readonly byte XorKey = 0x42;
        private static readonly char[] SubstitutionMap = {
            'a', 'z', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', 'q', 's', 'd', 'f', 'g', 'h',
            'j', 'k', 'l', 'm', 'w', 'x', 'c', 'v', 'b', 'n', 'A', 'Z', 'E', 'R', 'T', 'Y',
            'U', 'I', 'O', 'P', 'Q', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'W', 'X',
            'C', 'V', 'B', 'N', '0', '9', '8', '7', '6', '5', '4', '3', '2', '1', ' ', '!',
            '@', '#', '$', '%', '^', '&', '*', '(', ')', '-', '_', '=', '+', '[', ']', '{',
            '}', '|', '\\', ':', ';', '"', '\'', '<', '>', ',', '.', '?', '/', '~', '`'
        };
        public static string ObfuscateXor(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;
            byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            byte[] obfuscatedBytes = new byte[inputBytes.Length];
            for (int i = 0; i < inputBytes.Length; i++)
            {
                obfuscatedBytes[i] = (byte)(inputBytes[i] ^ XorKey);
            }
            return Convert.ToBase64String(obfuscatedBytes);
        }
        public static string DeobfuscateXor(string obfuscatedInput)
        {
            if (string.IsNullOrEmpty(obfuscatedInput))
                return string.Empty;
            try
            {
                byte[] obfuscatedBytes = Convert.FromBase64String(obfuscatedInput);
                byte[] deobfuscatedBytes = new byte[obfuscatedBytes.Length];
                for (int i = 0; i < obfuscatedBytes.Length; i++)
                {
                    deobfuscatedBytes[i] = (byte)(obfuscatedBytes[i] ^ XorKey);
                }
                return Encoding.UTF8.GetString(deobfuscatedBytes);
            }
            catch
            {
                return obfuscatedInput;
            }
        }
        public static string ObfuscateSubstitution(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;
            StringBuilder result = new StringBuilder();
            Random rand = new Random(42);
            foreach (char c in input)
            {
                if (char.IsLetterOrDigit(c) || char.IsPunctuation(c) || char.IsWhiteSpace(c))
                {
                    int index = rand.Next(0, SubstitutionMap.Length);
                    result.Append(SubstitutionMap[index]);
                }
                else
                {
                    result.Append(c);
                }
            }
            return result.ToString();
        }
        public static string ObfuscateRot13(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;
            StringBuilder result = new StringBuilder();
            foreach (char c in input)
            {
                if (char.IsLetter(c))
                {
                    char baseChar = char.IsUpper(c) ? 'A' : 'a';
                    result.Append((char)((c - baseChar + 13) % 26 + baseChar));
                }
                else
                {
                    result.Append(c);
                }
            }
            return result.ToString();
        }
        public static string DeobfuscateRot13(string obfuscatedInput)
        {
            return ObfuscateRot13(obfuscatedInput);
        }
        public static string ObfuscateChunkReverse(string input, int chunkSize = 4)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < input.Length; i += chunkSize)
            {
                int length = Math.Min(chunkSize, input.Length - i);
                string chunk = input.Substring(i, length);
                char[] chunkArray = chunk.ToCharArray();
                Array.Reverse(chunkArray);
                result.Append(new string(chunkArray));
            }
            return result.ToString();
        }
        public static string DeobfuscateChunkReverse(string obfuscatedInput, int chunkSize = 4)
        {
            return ObfuscateChunkReverse(obfuscatedInput, chunkSize);
        }
        public static string GetString(string obfuscatedString)
        {
            return DeobfuscateXor(obfuscatedString);
        }
        public static string PrepareString(string plainText)
        {
            return ObfuscateXor(plainText);
        }
    }
}