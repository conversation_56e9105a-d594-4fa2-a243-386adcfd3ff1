﻿using System;
using System.Collections.Generic;
using System.IO;
using AuthTool.lib.Interop;
namespace AuthTool.Commands
{
    public class ServiceReplace : IOperation
    {
        public static string CommandName => "replace";
        public void Execute(Dictionary<string, string> arguments)
        {
            Console.WriteLine("\r\n[*] Action: Service Token sname Substitution\r\n");
            string altservice = "";
            LUID luid = new LUID();
            bool ptt = false;
            if (arguments.ContainsKey("/luid"))
            {
                try
                {
                    luid = new LUID(arguments["/luid"]);
                }
                catch
                {
                    Console.WriteLine("[X] Invalid LUID format ({0})\r\n", arguments["/luid"]);
                    return;
                }
            }
            if (arguments.ContainsKey("/ptt"))
            {
                ptt = true;
            }
            if (arguments.ContainsKey("/altservice"))
            {
                altservice = arguments["/altservice"];
            }
            else
            {
                Console.WriteLine("\r\n[X] An /altservice:SNAME or /altservice:SNAME/host needs to be supplied!\r\n");
                return;
            }
            if (arguments.ContainsKey("/token"))
            {
                string kirbi64 = arguments["/token"];
                if (Helpers.IsBase64String(kirbi64))
                {
                    byte[] kirbiBytes = Convert.FromBase64String(kirbi64);
                    AUTH_CRED auth = new AUTH_CRED(kirbiBytes);
                    LSA.SubstituteTGSSname(auth, altservice, ptt, luid);
                }
                else if (File.Exists(kirbi64))
                {
                    byte[] kirbiBytes = File.ReadAllBytes(kirbi64);
                    AUTH_CRED auth = new AUTH_CRED(kirbiBytes);
                    LSA.SubstituteTGSSname(auth, altservice, ptt, luid);
                }
                else
                {
                    Console.WriteLine("\r\n[X]/token:X must either be a .auth file or a base64 encoded .auth\r\n");
                }
                return;
            }
            else
            {
                Console.WriteLine("\r\n[X] A /token:X needs to be supplied!\r\n");
                return;
            }
        }
    }
}