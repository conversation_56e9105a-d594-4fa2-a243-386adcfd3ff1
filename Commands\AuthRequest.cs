﻿using System;
using System.Collections.Generic;
using AuthTool;
using AuthTool.lib.Interop;
namespace AuthTool.Commands
{
    public class AuthRequest : IOperation
    {
        public static string CommandName => "request";
        public void Execute(Dictionary<string, string> arguments)
        {
            Console.WriteLine("[*] Action: Request authentication token");
            string user = "";
            string domain = "";
            string password = "";
            string hash = "";
            string dc = "";
            string outfile = "";
            string certificate = "";
            bool ptt = false;
            bool opsec = false;
            bool force = false;
            bool verifyCerts = false;
            LUID luid = new LUID();
            Interop.AUTH_ETYPE encType = Interop.AUTH_ETYPE.subkey_keymaterial;
            if (arguments.ContainsKey("/user"))
            {
                string[] parts = arguments["/user"].Split('\\');
                if (parts.Length == 2)
                {
                    domain = parts[0];
                    user = parts[1];
                }
                else
                {
                    user = arguments["/user"];
                }
            }
            if (arguments.ContainsKey("/domain"))
            {
                domain = arguments["/domain"];
            }
            if (arguments.ContainsKey("/dc"))
            {
                dc = arguments["/dc"];
            }
            if (arguments.ContainsKey("/outfile"))
            {
                outfile = arguments["/outfile"];
            }
            encType = Interop.AUTH_ETYPE.rc4_hmac;
            if (arguments.ContainsKey("/enctype")) {
                string encTypeString = arguments["/enctype"].ToUpper();
                if (encTypeString.Equals("RC4") || encTypeString.Equals("NTLM")) {
                    encType = Interop.AUTH_ETYPE.rc4_hmac;
                } else if (encTypeString.Equals("AES128")) {
                    encType = Interop.AUTH_ETYPE.aes128_cts_hmac_sha1;
                } else if (encTypeString.Equals("AES256") || encTypeString.Equals("AES")) {
                    encType = Interop.AUTH_ETYPE.aes256_cts_hmac_sha1;
                } else if (encTypeString.Equals("DES")) {
                    encType = Interop.AUTH_ETYPE.des_cbc_md5;
                }
            }
            if (arguments.ContainsKey("/password"))
            {
                password = arguments["/password"];
                string salt = String.Format("{0}{1}", domain.ToUpper(), user.ToLower());
                if (user.EndsWith("$"))
                {
                    salt = String.Format("{0}host{1}.{2}", domain.ToUpper(), user.TrimEnd('$').ToLower(), domain.ToLower());
                }
                hash = Crypto.AuthProtoPasswordHash(encType, password, salt);
            }
            else if (arguments.ContainsKey("/des"))
            {
                hash = arguments["/des"];
                encType = Interop.AUTH_ETYPE.des_cbc_md5;
            }
            else if (arguments.ContainsKey("/rc4"))
            {
                hash = arguments["/rc4"];
                encType = Interop.AUTH_ETYPE.rc4_hmac;
            }
            else if (arguments.ContainsKey("/ntlm"))
            {
                hash = arguments["/ntlm"];
                encType = Interop.AUTH_ETYPE.rc4_hmac;
            }
            else if (arguments.ContainsKey("/aes128"))
            {
                hash = arguments["/aes128"];
                encType = Interop.AUTH_ETYPE.aes128_cts_hmac_sha1;
            }
            else if (arguments.ContainsKey("/aes256"))
            {
                hash = arguments["/aes256"];
                encType = Interop.AUTH_ETYPE.aes256_cts_hmac_sha1;
            }
            if (arguments.ContainsKey("/certificate")) {
                certificate = arguments["/certificate"];
                if(arguments.ContainsKey("/verifychain") || arguments.ContainsKey("/verifycerts"))
                {
                    Console.WriteLine("[*] Verifying the entire certificate chain!\r\n");
                    verifyCerts = true;
                }
            }
            if (arguments.ContainsKey("/ptt"))
            {
                ptt = true;
            }
            if (arguments.ContainsKey("/opsec"))
            {
                opsec = true;
            }
            if (arguments.ContainsKey("/force"))
            {
                force = true;
            }
            if (arguments.ContainsKey("/luid"))
            {
                try
                {
                    luid = new LUID(arguments["/luid"]);
                }
                catch
                {
                    Console.WriteLine("[X] Invalid LUID format ({0})\r\n", arguments["/luid"]);
                    return;
                }
            }
            if (arguments.ContainsKey("/createnetonly"))
            {
                if (!Helpers.IsHighIntegrity())
                {
                    Console.WriteLine("[X] You need to be in high integrity to apply a token to created logon session");
                    return;
                }
                if (arguments.ContainsKey("/show"))
                {
                    luid = Helpers.CreateProcessNetOnly(arguments["/createnetonly"], true);
                }
                else
                {
                    luid = Helpers.CreateProcessNetOnly(arguments["/createnetonly"], false);
                }
                Console.WriteLine();
            }
            if (String.IsNullOrEmpty(user))
            {
                Console.WriteLine("\r\n[X] You must supply a user name!\r\n");
                return;
            }
            if (String.IsNullOrEmpty(domain))
            {
                domain = System.Net.NetworkInformation.IPGlobalProperties.GetIPGlobalProperties().DomainName;
            }
            if (String.IsNullOrEmpty(hash) && String.IsNullOrEmpty(certificate))
            {
                Console.WriteLine("\r\n[X] You must supply a /password, /certificate or a [/des|/rc4|/aes128|/aes256] hash!\r\n");
                return;
            }
            if (!((encType == Interop.AUTH_ETYPE.des_cbc_md5) || (encType == Interop.AUTH_ETYPE.rc4_hmac) || (encType == Interop.AUTH_ETYPE.aes128_cts_hmac_sha1) || (encType == Interop.AUTH_ETYPE.aes256_cts_hmac_sha1)))
            {
                Console.WriteLine("\r\n[X] Only /des, /rc4, /aes128, and /aes256 are supported at this time.\r\n");
                return;
            }
            else
            {
                if ((opsec) && (encType != Interop.AUTH_ETYPE.aes256_cts_hmac_sha1) && !(force))
                {
                    Console.WriteLine("[X] Using /opsec but not using /enctype:aes256, to force this behaviour use /force");
                    return;
                }
                if (String.IsNullOrEmpty(certificate))
                    Ask.TKN(user, domain, hash, encType, outfile, ptt, dc, luid, true, opsec);
                else
                    Ask.TKN(user, domain, certificate, password, encType, outfile, ptt, dc, luid, true, verifyCerts);
                return;
            }
        }
    }
}