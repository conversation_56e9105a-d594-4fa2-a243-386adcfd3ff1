﻿using System;
using System.Collections.Generic;
using System.IO;
namespace AuthTool.Commands
{
    public class TokenRefresh : IOperation
    {
        public static string CommandName => "refresh";
        public void Execute(Dictionary<string, string> arguments)
        {
            string outfile = "";
            bool ptt = false;
            string dc = "";
            if (arguments.ContainsKey("/outfile"))
            {
                outfile = arguments["/outfile"];
            }
            if (arguments.ContainsKey("/ptt"))
            {
                ptt = true;
            }
            if (arguments.ContainsKey("/dc"))
            {
                dc = arguments["/dc"];
            }
            if (arguments.ContainsKey("/token"))
            {
                string auth64 = arguments["/token"];
                byte[] authBytes = null;
                if (Helpers.IsBase64String(auth64))
                {
                    authBytes = Convert.FromBase64String(auth64);
                }
                else if (File.Exists(auth64))
                {
                    authBytes = File.ReadAllBytes(auth64);
                }
                if(authBytes == null)
                {
                    Console.WriteLine("\r\n[X] /token:X must either be a .auth file or a base64 encoded .auth\r\n");
                }
                else
                {
                    AUTH_CRED auth = new AUTH_CRED(authBytes);
                    if (arguments.ContainsKey("/autorenew"))
                    {
                        Console.WriteLine("[*] Action: Auto-Renew Token\r\n");
                        Renew.TKNAutoRenew(auth, dc);
                    }
                    else
                    {
                        Console.WriteLine("[*] Action: Renew Token\r\n");
                        byte[] blah = Renew.TKN(auth, outfile, ptt, dc);
                    }
                }
                return;
            }
            else
            {
                Console.WriteLine("\r\n[X] A /token:X needs to be supplied!\r\n");
                return;
            }
        }
    }
}