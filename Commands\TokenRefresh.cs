﻿using System;
using System.Collections.Generic;
using System.IO;
namespace AuthTool.Commands
{
    public class TokenRefresh : IOperation
    {
        public static string CommandName => "refresh";
        public void Execute(Dictionary<string, string> arguments)
        {
            string outfile = "";
            bool ptt = false;
            string dc = "";
            if (arguments.ContainsKey("/outfile"))
            {
                outfile = arguments["/outfile"];
            }
            if (arguments.ContainsKey("/ptt"))
            {
                ptt = true;
            }
            if (arguments.ContainsKey("/dc"))
            {
                dc = arguments["/dc"];
            }
            if (arguments.ContainsKey("/token"))
            {
                string kirbi64 = arguments["/token"];
                byte[] kirbiBytes = null;
                if (Helpers.IsBase64String(kirbi64))
                {
                    kirbiBytes = Convert.FromBase64String(kirbi64);
                }
                else if (File.Exists(kirbi64))
                {
                    kirbiBytes = File.ReadAllBytes(kirbi64);
                }
                if(kirbiBytes == null)
                {
                    Console.WriteLine("\r\n[X] /token:X must either be a .auth file or a base64 encoded .auth\r\n");
                }
                else
                {
                    AUTH_CRED auth = new AUTH_CRED(kirbiBytes);
                    if (arguments.ContainsKey("/autorenew"))
                    {
                        Console.WriteLine("[*] Action: Auto-Renew Token\r\n");
                        Renew.TGTAutoRenew(auth, dc);
                    }
                    else
                    {
                        Console.WriteLine("[*] Action: Renew Token\r\n");
                        byte[] blah = Renew.TKN(auth, outfile, ptt, dc);
                    }
                }
                return;
            }
            else
            {
                Console.WriteLine("\r\n[X] A /token:X needs to be supplied!\r\n");
                return;
            }
        }
    }
}