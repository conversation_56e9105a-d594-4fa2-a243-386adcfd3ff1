﻿using System;
using System.Collections.Generic;
using AuthTool.Commands;

namespace AuthTool.Domain
{
    public class OperationRegistry
    {
        private readonly Dictionary<string, Func<IOperation>> _availableOperations = new Dictionary<string, Func<IOperation>>();

        // How To Add A New Operation:
        //  1. Create your operation class in the Commands Folder
        //      a. That class must have a CommandName static property that has the Operation's name
        //              and must also Implement the IOperation interface
        //      b. Put the code that does the work into the Execute() method
        //  2. Add an entry to the _availableOperations dictionary in the Constructor below.

        public OperationRegistry()
        {
            // Using obfuscated operation names
            _availableOperations.Add(ServiceQuery.CommandName, () => new ServiceQuery());
            _availableOperations.Add(AuthRequest.CommandName, () => new AuthRequest());
            _availableOperations.Add(UserScan.CommandName, () => new UserScan());
            _availableOperations.Add(PassModify.CommandName, () => new PassModify());
            _availableOperations.Add(ProcessSpawn.CommandName, () => new ProcessSpawn());
            _availableOperations.Add(SessionInfo.CommandName, () => new SessionInfo());
            _availableOperations.Add(DataParser.CommandName, () => new DataParser());
            _availableOperations.Add(DataExport.CommandName, () => new DataExport());
            _availableOperations.Add(CryptoUtil.CommandName, () => new CryptoUtil());
            _availableOperations.Add(DataCollect.CommandName, () => new DataCollect());
            _availableOperations.Add(ResourceEnum.CommandName, () => new ResourceEnum());
            _availableOperations.Add(SessionList.CommandName, () => new SessionList());
            _availableOperations.Add(EventWatch.CommandName, () => new EventWatch());
            _availableOperations.Add(TokenInject.CommandName, () => new TokenInject());
            _availableOperations.Add(SessionClear.CommandName, () => new SessionClear());
            _availableOperations.Add(TokenRefresh.CommandName, () => new TokenRefresh());
            _availableOperations.Add(DelegateAuth.CommandName, () => new DelegateAuth());
            _availableOperations.Add(ServiceReplace.CommandName, () => new ServiceReplace());
            _availableOperations.Add(TokenObtain.CommandName, () => new TokenObtain());
            _availableOperations.Add(SessionSurvey.CommandName, () => new SessionSurvey());
            _availableOperations.Add(CredTest.CommandName, () => new CredTest());
            _availableOperations.Add(TokenForge.CommandName, () => new TokenForge());
        }

        public bool ExecuteOperation(string operationName, Dictionary<string, string> arguments)
        {
            bool operationWasFound;

            if (string.IsNullOrEmpty(operationName) || _availableOperations.ContainsKey(operationName) == false)
                operationWasFound= false;
            else
            {
                // Create the operation object
                var operation = _availableOperations[operationName].Invoke();

                // and execute it with the arguments from the command line
                operation.Execute(arguments);

                operationWasFound = true;
            }

            return operationWasFound;
        }
    }
}