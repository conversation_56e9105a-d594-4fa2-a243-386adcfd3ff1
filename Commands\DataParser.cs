﻿using System;
using System.Collections.Generic;
using System.IO;
namespace AuthTool.Commands
{
    public class DataParser : IOperation
    {
        public static string CommandName => "info";
        public void Execute(Dictionary<string, string> arguments)
        {
            Console.WriteLine("\r\n[*] Action: Describe Token\r\n");
            if (arguments.ContainsKey("/token"))
            {
                string auth64 = arguments["/token"];
                if (Helpers.IsBase64String(auth64))
                {
                    byte[] authBytes = Convert.FromBase64String(auth64);
                    AUTH_CRED auth = new AUTH_CRED(authBytes);
                    LSA.DisplayToken(auth, 2, false, false, true);
                }
                else if (File.Exists(auth64))
                {
                    byte[] authBytes = File.ReadAllBytes(auth64);
                    AUTH_CRED auth = new AUTH_CRED(authBytes);
                    LSA.DisplayToken(auth, 2, false, false, true);
                }
                else
                {
                    Console.WriteLine("\r\n[X] /token:X must either be a .auth file or a base64 encoded .auth\r\n");
                }
                return;
            }
            else
            {
                Console.WriteLine("\r\n[X] A /token:X needs to be supplied!\r\n");
                return;
            }
        }
    }
}