using static AuthTool.Interop;
﻿using System;
using Asn1;
using System.Text;
using System.Collections.Generic;
namespace AuthTool
{
    public class Checksum
    {
        public Checksum(byte[] data)
        {
            cksumtype = -138;
            checksum = data;
        }
        public Checksum(Interop.KERB_CHECKSUM_ALGORITHM cktype, byte[] data)
        {
            cksumtype = (int)cktype;
            checksum = data;
        }
        public Checksum(AsnElt body)
        {
            foreach (AsnElt s in body.Sub)
            {
                switch (s.TagValue)
                {
                    case 0:
                        cksumtype = Convert.ToInt32(s.Sub[0].GetInteger());
                        break;
                    case 2:
                        checksum = s.Sub[0].GetOctetString();
                        break;
                    default:
                        break;
                }
            }
        }
        public AsnElt Encode()
        {
            AsnElt cksumtypeAsn = AsnElt.MakeInteger(cksumtype);
            AsnElt cksumtypeSeq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { cksumtypeAsn });
            cksumtypeSeq = AsnElt.MakeImplicit(AsnElt.CONTEXT, 0, cksumtypeSeq);
            AsnElt checksumAsn = AsnElt.MakeBlob(checksum);
            AsnElt checksumSeq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { checksumAsn });
            checksumSeq = AsnElt.MakeImplicit(AsnElt.CONTEXT, 1, checksumSeq);
            AsnElt totalSeq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { cksumtypeSeq, checksumSeq });
            AsnElt totalSeq2 = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { totalSeq });
            return totalSeq2;
        }
        public Int32 cksumtype { get; set; }
        public byte[] checksum { get; set; }
    }
}