﻿using System;
using System.Collections.Generic;
using System.IO;
namespace AuthTool.Commands
{
    public class PassModify : IOperation
    {
        public static string CommandName => "modify";
        public void Execute(Dictionary<string, string> arguments)
        {
            Console.WriteLine("[*] Action: Reset User Password (AoratoPw)\r\n");
            string newPassword = "";
            string dc = "";
            if (arguments.ContainsKey("/new"))
            {
                newPassword = arguments["/new"];
            }
            if (String.IsNullOrEmpty(newPassword))
            {
                Console.WriteLine("\r\n[X] New password must be supplied with /new:X !\r\n");
                return;
            }
            if (arguments.ContainsKey("/dc"))
            {
                dc = arguments["/dc"];
            }
            if (arguments.ContainsKey("/token"))
            {
                string auth64 = arguments["/token"];
                if (Helpers.IsBase64String(auth64))
                {
                    byte[] authBytes = Convert.FromBase64String(auth64);
                    AUTH_CRED auth = new AUTH_CRED(authBytes);
                    Reset.UserPassword(auth, newPassword, dc);
                }
                else if (File.Exists(auth64))
                {
                    byte[] authBytes = File.ReadAllBytes(auth64);
                    AUTH_CRED auth = new AUTH_CRED(authBytes);
                    Reset.UserPassword(auth, newPassword, dc);
                }
                else
                {
                    Console.WriteLine("\r\n[X]/token:X must either be a .auth file or a base64 encoded .auth\r\n");
                }
                return;
            }
            else
            {
                Console.WriteLine("\r\n[X] A /token:X needs to be supplied!\r\n");
                return;
            }
        }
    }
}