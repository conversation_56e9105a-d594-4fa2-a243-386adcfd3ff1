﻿using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Text;
namespace AuthTool.Commands
{
    public class ResourceEnum : IOperation
    {
        public static string CommandName => "enumerate";
        public void Execute(Dictionary<string, string> arguments)
        {
            Console.WriteLine("[*] Action: Enumerating resources");
            string spn = "";
            List<string> spns = null;
            string user = "";
            string OU = "";
            string outFile = "";
            string domain = "";
            string dc = "";
            string ldapFilter = "";
            string supportedEType = "rc4";
            bool useTKNdeleg = false;
            bool listUsers = false;
            AUTH_CRED TKN = null;
            string pwdSetAfter = "";
            string pwdSetBefore = "";
            int resultLimit = 0;
            int delay = 0;
            int jitter = 0;
            bool simpleOutput = false;
            bool enterprise = false;
            bool autoenterprise = false;
            if (arguments.ContainsKey("/spn"))
            {
                spn = arguments["/spn"];
            }
            if (arguments.ContainsKey("/spns"))
            {
                spns = new List<string>();
                if (System.IO.File.Exists(arguments["/spns"]))
                {
                    string fileContent = Encoding.UTF8.GetString(System.IO.File.ReadAllBytes(arguments["/spns"]));
                    foreach (string s in fileContent.Split('\n'))
                    {
                        if (!String.IsNullOrEmpty(s))
                        {
                            spns.Add(s.Trim());
                        }
                    }
                }
                else
                {
                    foreach (string s in arguments["/spns"].Split(','))
                    {
                        spns.Add(s);
                    }
                }
            }
            if (arguments.ContainsKey("/user"))
            {
                user = arguments["/user"];
            }
            if (arguments.ContainsKey("/ou"))
            {
                OU = arguments["/ou"];
            }
            if (arguments.ContainsKey("/domain"))
            {
                domain = arguments["/domain"];
            }
            if (arguments.ContainsKey("/dc"))
            {
                dc = arguments["/dc"];
            }
            if (arguments.ContainsKey("/outfile"))
            {
                outFile = arguments["/outfile"];
            }
            if (arguments.ContainsKey("/simple"))
            {
                simpleOutput = true;
            }
            if (arguments.ContainsKey("/aes"))
            {
                supportedEType = "aes";
            }
            if (arguments.ContainsKey("/rc4opsec"))
            {
                supportedEType = "rc4opsec";
            }
            if (arguments.ContainsKey("/token"))
            {
                string auth64 = arguments["/token"];
                if (Helpers.IsBase64String(auth64))
                {
                    byte[] authBytes = Convert.FromBase64String(auth64);
                    TKN = new AUTH_CRED(authBytes);
                }
                else if (System.IO.File.Exists(auth64))
                {
                    byte[] authBytes = System.IO.File.ReadAllBytes(auth64);
                    TKN = new AUTH_CRED(authBytes);
                }
                else
                {
                    Console.WriteLine("\r\n[X] /token:X must either be a .auth file or a base64 encoded .auth\r\n");
                }
            }
            if (arguments.ContainsKey("/usetkndeleg") || arguments.ContainsKey("/tkndeleg"))
            {
                useTKNdeleg = true;
            }
            if (arguments.ContainsKey("/pwdsetafter"))
            {
                pwdSetAfter = arguments["/pwdsetafter"];
            }
            if (arguments.ContainsKey("/pwdsetbefore"))
            {
                pwdSetBefore = arguments["/pwdsetbefore"];
            }
            if (arguments.ContainsKey("/ldapfilter"))
            {
                ldapFilter = arguments["/ldapfilter"].Trim('"').Trim('\'');
            }
            if (arguments.ContainsKey("/resultlimit"))
            {
                resultLimit = Convert.ToInt32(arguments["/resultlimit"]);
            }
            if (arguments.ContainsKey("/delay"))
            {
                delay = Int32.Parse(arguments["/delay"]);
                if(delay < 100)
                {
                    Console.WriteLine("[!] WARNING: delay is in milliseconds! Please enter a value > 100.");
                    return;
                }
            }
            if (arguments.ContainsKey("/jitter"))
            {
                try
                {
                    jitter = Int32.Parse(arguments["/jitter"]);
                }
                catch {
                    Console.WriteLine("[X] Jitter must be an integer between 1-100.");
                    return;
                }
                if(jitter <= 0 || jitter > 100)
                {
                    Console.WriteLine("[X] Jitter must be between 1-100");
                    return;
                }
            }
            if (arguments.ContainsKey("/stats"))
            {
                listUsers = true;
            }
            if (arguments.ContainsKey("/enterprise"))
            {
                enterprise = true;
            }
            if (arguments.ContainsKey("/autoenterprise"))
            {
                autoenterprise = true;
            }
            if (arguments.ContainsKey("/creduser"))
            {
                if (!Regex.IsMatch(arguments["/creduser"], ".+\\.+", RegexOptions.IgnoreCase))
                {
                    Console.WriteLine("\r\n[X] /creduser specification must be in fqdn format (domain.com\\user)\r\n");
                    return;
                }
                string[] parts = arguments["/creduser"].Split('\\');
                string domainName = parts[0];
                string userName = parts[1];
                if (!arguments.ContainsKey("/credpassword"))
                {
                    Console.WriteLine("\r\n[X] /credpassword is required when specifying /creduser\r\n");
                    return;
                }
                string password = arguments["/credpassword"];
                System.Net.NetworkCredential cred = new System.Net.NetworkCredential(userName, password, domainName);
                Roast.Kerberoast(spn, spns, user, OU, domain, dc, cred, outFile, simpleOutput, TKN, useTKNdeleg, supportedEType, pwdSetAfter, pwdSetBefore, ldapFilter, resultLimit, delay, jitter, listUsers, enterprise, autoenterprise);
            }
            else
            {
                Roast.Kerberoast(spn, spns, user, OU, domain, dc, null, outFile, simpleOutput, TKN, useTKNdeleg, supportedEType, pwdSetAfter, pwdSetBefore, ldapFilter, resultLimit, delay, jitter, listUsers, enterprise, autoenterprise);
            }
        }
    }
}