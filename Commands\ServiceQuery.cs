﻿using System;
using System.Collections.Generic;
using System.IO;
namespace AuthTool.Commands
{
    public class ServiceQuery : IOperation
    {
        public static string CommandName => "extract";
        public void Execute(Dictionary<string, string> arguments)
        {
            Console.WriteLine("[*] Action: Ask SVC\r\n");
            string outfile = "";
            bool ptt = false;
            string dc = "";
            string service = "";
            bool enterprise = false;
            bool opsec = false;
            bool force = false;
            Interop.AUTH_ETYPE requestEnctype = Interop.AUTH_ETYPE.subkey_keymaterial;
            AUTH_CRED svc = null;
            bool usesvcdomain = false;
            if (arguments.ContainsKey("/outfile"))
            {
                outfile = arguments["/outfile"];
            }
            if (arguments.ContainsKey("/ptt"))
            {
                ptt = true;
            }
            if (arguments.ContainsKey("/enterprise"))
            {
                enterprise = true;
            }
            if (arguments.ContainsKey("/opsec"))
            {
                opsec = true;
            }
            if (arguments.ContainsKey("/force"))
            {
                force = true;
            }
            if (arguments.ContainsKey("/dc"))
            {
                dc = arguments["/dc"];
            }
            if (arguments.ContainsKey("/enctype"))
            {
                string encTypeString = arguments["/enctype"].ToUpper();
                if (encTypeString.Equals("RC4") || encTypeString.Equals("NTLM"))
                {
                    requestEnctype = Interop.AUTH_ETYPE.rc4_hmac;
                }
                else if (encTypeString.Equals("AES128"))
                {
                    requestEnctype = Interop.AUTH_ETYPE.aes128_cts_hmac_sha1;
                }
                else if (encTypeString.Equals("AES256") || encTypeString.Equals("AES"))
                {
                    requestEnctype = Interop.AUTH_ETYPE.aes256_cts_hmac_sha1;
                }
                else if (encTypeString.Equals("DES"))
                {
                    requestEnctype = Interop.AUTH_ETYPE.des_cbc_md5;
                }
                else
                {
                    Console.WriteLine("Unsupported etype : {0}", encTypeString);
                    return;
                }
            }
            if (arguments.ContainsKey("/service"))
            {
                service = arguments["/service"];
            }
            else
            {
                Console.WriteLine("[X] One or more '/service:sname/server.domain.com' specifications are needed");
                return;
            }
            if ((opsec) && (requestEnctype != Interop.AUTH_ETYPE.aes256_cts_hmac_sha1) && !(force))
            {
                Console.WriteLine("[X] Using /opsec but not using /enctype:aes256, to force this behaviour use /force");
                return;
            }
            if (arguments.ContainsKey("/svc"))
            {
                string auth64 = arguments["/svc"];
                if (Helpers.IsBase64String(auth64))
                {
                    byte[] authBytes = Convert.FromBase64String(auth64);
                    svc = new AUTH_CRED(authBytes);
                }
                else if (File.Exists(auth64))
                {
                    byte[] authBytes = File.ReadAllBytes(auth64);
                    svc = new AUTH_CRED(authBytes);
                }
                else
                {
                    Console.WriteLine("\r\n[X] /svc:X must either be a .auth file or a base64 encoded .auth\r\n");
                    return;
                }
                if (arguments.ContainsKey("/usesvcdomain"))
                {
                    usesvcdomain = true;
                }
            }
            if (arguments.ContainsKey("/token"))
            {
                string auth64 = arguments["/token"];
                if (Helpers.IsBase64String(auth64))
                {
                    byte[] authBytes = Convert.FromBase64String(auth64);
                    AUTH_CRED auth = new AUTH_CRED(authBytes);
                    Ask.SVC(auth, service, requestEnctype, outfile, ptt, dc, true, enterprise, false, opsec, svc, usesvcdomain);
                    return;
                }
                else if (File.Exists(auth64))
                {
                    byte[] authBytes = File.ReadAllBytes(auth64);
                    AUTH_CRED auth = new AUTH_CRED(authBytes);
                    Ask.SVC(auth, service, requestEnctype, outfile, ptt, dc, true, enterprise, false, opsec, svc, usesvcdomain);
                    return;
                }
                else
                {
                    Console.WriteLine("\r\n[X] /token:X must either be a .auth file or a base64 encoded .auth\r\n");
                }
                return;
            }
            else
            {
                Console.WriteLine("\r\n[X] A /token:X needs to be supplied!\r\n");
                return;
            }
        }
    }
}