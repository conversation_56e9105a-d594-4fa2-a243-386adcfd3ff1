﻿using System.Collections.Generic;
namespace AuthTool.Domain
{
    public class ParseResult
    {
        public bool ParsedOk { get; }
        public Dictionary<string, string> Arguments { get; }
        private ParseResult(bool parsedOk, Dictionary<string, string> arguments)
        {
            ParsedOk = parsedOk;
            Arguments = arguments;
        }
        public static ParseResult Success(Dictionary<string, string> arguments)
            => new ParseResult(true, arguments);
        public static ParseResult Failure()
            => new ParseResult(false, null);
    }
}