﻿using System;
using Asn1;
using System.Text;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using AuthTool.lib.Interop;
namespace AuthTool
{
    public class Interop
    {
        public const int AUTH_KEY_USAGE_AUTH_REQ_PA_ENC_TIMESTAMP = 1;
        public const int AUTH_KEY_USAGE_AUTH_REP_SVC_REP = 2;
        public const int AUTH_KEY_USAGE_AUTH_REP_EP_SESSION_KEY = 3;
        public const int AUTH_KEY_USAGE_SVC_REQ_ENC_AUTHOIRZATION_DATA = 4;
        public const int AUTH_KEY_USAGE_SVC_REQ_PA_AUTHENTICATOR = 7;
        public const int AUTH_KEY_USAGE_SVC_REP_EP_SESSION_KEY = 8;
        public const int AUTH_KEY_USAGE_AP_REQ_AUTHENTICATOR = 11;
        public const int AUTH_KEY_USAGE_AUTH_PRIV_ENCRYPTED_PART = 13;
        public const int AUTH_KEY_USAGE_AUTH_CRED_ENCRYPTED_PART = 14;
        [Flags]
        public enum TokenFlags : UInt32
        {
            reserved = 2147483648,
            forwardable = 0x40000000,
            forwarded = 0x20000000,
            proxiable = 0x10000000,
            proxy = 0x08000000,
            may_postdate = 0x04000000,
            postdated = 0x02000000,
            invalid = 0x01000000,
            renewable = 0x00800000,
            initial = 0x00400000,
            pre_authent = 0x00200000,
            hw_authent = 0x00100000,
            ok_as_delegate = 0x00040000,
            name_canonicalize = 0x00010000,
            enc_pa_rep = 0x00010000,
            reserved1 = 0x00000001
        }
        [Flags]
        public enum KdcOptions : uint
        {
            VALIDATE = 0x00000001,
            RENEW = 0x00000002,
            UNUSED29 = 0x00000004,
            ENCTKTINSKEY = 0x00000008,
            RENEWABLEOK = 0x00000010,
            DISABLETRANSITEDCHECK = 0x00000020,
            UNUSED16 = 0x0000FFC0,
            CONSTRAINED_DELEGATION = 0x00020000,
            CANONICALIZE = 0x00010000,
            CNAMEINADDLTKT = 0x00004000,
            OK_AS_DELEGATE = 0x00040000,
            UNUSED12 = 0x00080000,
            OPTHARDWAREAUTH = 0x00100000,
            PREAUTHENT = 0x00200000,
            INITIAL = 0x00400000,
            RENEWABLE = 0x00800000,
            UNUSED7 = 0x01000000,
            POSTDATED = 0x02000000,
            ALLOWPOSTDATE = 0x04000000,
            PROXY = 0x08000000,
            PROXIABLE = 0x10000000,
            FORWARDED = 0x20000000,
            FORWARDABLE = 0x40000000,
            RESERVED = 0x80000000
        }
        public enum AUTH_MESSAGE_TYPE : long
        {
            AUTH_REQ = 10,
            AUTH_REP = 11,
            SVC_REQ = 12,
            SVC_REP = 13,
            AP_REQ = 14,
            AP_REP = 15,
            TGT_REQ = 16,
            TGT_REP = 17,
            SAFE = 20,
            PRIV = 21,
            CRED = 22,
            ERROR = 30
        }
        public enum AUTH_ETYPE : Int32
        {
            des_cbc_crc = 1,
            des_cbc_md4 = 2,
            des_cbc_md5 = 3,
            des3_cbc_md5 = 5,
            des3_cbc_sha1 = 7,
            dsaWithSHA1_CmsOID = 9,
            md5WithRSAEncryption_CmsOID = 10,
            sha1WithRSAEncryption_CmsOID = 11,
            rc2CBC_EnvOID = 12,
            rsaEncryption_EnvOID = 13,
            rsaES_OAEP_ENV_OID = 14,
            des_ede3_cbc_Env_OID = 15,
            des3_cbc_sha1_kd = 16,
            aes128_cts_hmac_sha1 = 17,
            aes256_cts_hmac_sha1 = 18,
            rc4_hmac = 23,
            rc4_hmac_exp = 24,
            subkey_keymaterial = 65,
            old_exp = -135
        }
        [Flags]
        public enum SUPPORTED_ETYPE : Int32
        {
            RC4_HMAC_DEFAULT = 0x0,
            DES_CBC_CRC = 0x1,
            DES_CBC_MD5 = 0x2,
            RC4_HMAC = 0x4,
            AES128_CTS_HMAC_SHA1_96 = 0x08,
            AES256_CTS_HMAC_SHA1_96 = 0x10
        }
        public enum KADMIN_PASSWD_ERR : UInt32
        {
            KRB5_KPASSWD_SUCCESS = 0,
            KRB5_KPASSWD_MALFORMED = 1,
            KRB5_KPASSWD_HARDERROR = 2,
            KRB5_KPASSWD_AUTHERROR = 3,
            KRB5_KPASSWD_SOFTERROR = 4,
            KRB5_KPASSWD_ACCESSDENIED = 5,
            KRB5_KPASSWD_BAD_VERSION = 6,
            KRB5_KPASSWD_INITIAL_FLAG_NEEDED = 7
        }
        public enum AUTH_CHECKSUM_ALGORITHM
        {
            AUTH_CHECKSUM_RSA_MD5 = 7,
            AUTH_CHECKSUM_HMAC_SHA1_96_AES128 = 15,
            AUTH_CHECKSUM_HMAC_SHA1_96_AES256 = 16,
            AUTH_CHECKSUM_HMAC_SHA1_96_AES256_X509 = 26,
            AUTH_CHECKSUM_DES_MAC = -133,
            AUTH_CHECKSUM_HMAC_MD5 = -138,
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_CHECKSUM
        {
            public int Type;
            public int Size;
            public int Flag;
            public IntPtr Initialize;
            public IntPtr Sum;
            public IntPtr Finalize;
            public IntPtr Finish;
            public IntPtr InitializeEx;
            public IntPtr unk0_null;
        }
        public enum PRINCIPAL_TYPE : long
        {
            NT_UNKNOWN = 0,
            NT_PRINCIPAL = 1,
            NT_SRV_INST = 2,
            NT_SRV_HST = 3,
            NT_SRV_XHST = 4,
            NT_UID = 5,
            NT_X500_PRINCIPAL = 6,
            NT_SMTP_NAME = 7,
            NT_ENTERPRISE = 10
        }
        public enum PADATA_TYPE : UInt32
        {
            NONE = 0,
            SVC_REQ = 1,
            AP_REQ = 1,
            ENC_TIMESTAMP = 2,
            PW_SALT = 3,
            ENC_UNIX_TIME = 5,
            SANDIA_SECUREID = 6,
            SESAME = 7,
            OSF_DCE = 8,
            CYBERSAFE_SECUREID = 9,
            AFS3_SALT = 10,
            ETYPE_INFO = 11,
            SAM_CHALLENGE = 12,
            SAM_RESPONSE = 13,
            PK_AUTH_REQ_19 = 14,
            PK_AUTH_REP_19 = 15,
            PK_AUTH_REQ_WIN = 15,
            PK_AUTH_REQ = 16,
            PK_AUTH_REP = 17,
            PA_PK_OCSP_RESPONSE = 18,
            ETYPE_INFO2 = 19,
            USE_SPECIFIED_KVNO = 20,
            SVR_REFERRAL_INFO = 20,
            SAM_REDIRECT = 21,
            GET_FROM_TYPED_DATA = 22,
            SAM_ETYPE_INFO = 23,
            SERVER_REFERRAL = 25,
            TD_AUTH_PRINCIPAL = 102,
            PK_TD_TRUSTED_CERTIFIERS = 104,
            PK_TD_CERTIFICATE_INDEX = 105,
            TD_APP_DEFINED_ERROR = 106,
            TD_REQ_NONCE = 107,
            TD_REQ_SEQ = 108,
            PA_PAC_REQUEST = 128,
            S4U2SELF = 129,
            PA_S4U_X509_USER = 130,
            PA_PAC_OPTIONS = 167,
            PK_AS_09_BINDING = 132,
            CLIENT_CANONICALIZED = 133
        }
        public enum PA_S4U_X509_USER_OPTIONS : Int32
        {
            CHECK_LOGON_RESTRICTIONS = 0x40000000,
            SIGN_REPLY = 0x20000000
        }
        public enum KERBEROS_ERROR : UInt32
        {
            KDC_ERR_NONE = 0x0,
            KDC_ERR_NAME_EXP = 0x1,
            KDC_ERR_SERVICE_EXP = 0x2,
            KDC_ERR_BAD_PVNO = 0x3,
            KDC_ERR_C_OLD_MAST_KVNO = 0x4,
            KDC_ERR_S_OLD_MAST_KVNO = 0x5,
            KDC_ERR_C_PRINCIPAL_UNKNOWN = 0x6,
            KDC_ERR_S_PRINCIPAL_UNKNOWN = 0x7,
            KDC_ERR_PRINCIPAL_NOT_UNIQUE = 0x8,
            KDC_ERR_NULL_KEY = 0x9,
            KDC_ERR_CANNOT_POSTDATE = 0xA,
            KDC_ERR_NEVER_VALID = 0xB,
            KDC_ERR_POLICY = 0xC,
            KDC_ERR_BADOPTION = 0xD,
            KDC_ERR_ETYPE_NOTSUPP = 0xE,
            KDC_ERR_SUMTYPE_NOSUPP = 0xF,
            KDC_ERR_PADATA_TYPE_NOSUPP = 0x10,
            KDC_ERR_TRTYPE_NO_SUPP = 0x11,
            KDC_ERR_CLIENT_REVOKED = 0x12,
            KDC_ERR_SERVICE_REVOKED = 0x13,
            KDC_ERR_TGT_REVOKED = 0x14,
            KDC_ERR_CLIENT_NOTYET = 0x15,
            KDC_ERR_SERVICE_NOTYET = 0x16,
            KDC_ERR_KEY_EXPIRED = 0x17,
            KDC_ERR_PREAUTH_FAILED = 0x18,
            KDC_ERR_PREAUTH_REQUIRED = 0x19,
            KDC_ERR_SERVER_NOMATCH = 0x1A,
            KDC_ERR_SVC_UNAVAILABLE = 0x1B,
            AUTH_AP_ERR_BAD_INTEGRITY = 0x1F,
            AUTH_AP_ERR_TKT_EXPIRED = 0x20,
            AUTH_AP_ERR_TKT_NYV = 0x21,
            AUTH_AP_ERR_REPEAT = 0x22,
            AUTH_AP_ERR_NOT_US = 0x23,
            AUTH_AP_ERR_BADMATCH = 0x24,
            AUTH_AP_ERR_SKEW = 0x25,
            AUTH_AP_ERR_BADADDR = 0x26,
            AUTH_AP_ERR_BADVERSION = 0x27,
            AUTH_AP_ERR_MSG_TYPE = 0x28,
            AUTH_AP_ERR_MODIFIED = 0x29,
            AUTH_AP_ERR_BADORDER = 0x2A,
            AUTH_AP_ERR_BADKEYVER = 0x2C,
            AUTH_AP_ERR_NOKEY = 0x2D,
            AUTH_AP_ERR_MUT_FAIL = 0x2E,
            AUTH_AP_ERR_BADDIRECTION = 0x2F,
            AUTH_AP_ERR_METHOD = 0x30,
            AUTH_AP_ERR_BADSEQ = 0x31,
            AUTH_AP_ERR_INAPP_CKSUM = 0x32,
            AUTH_AP_PATH_NOT_ACCEPTED = 0x33,
            AUTH_ERR_RESPONSE_TOO_BIG = 0x34,
            AUTH_ERR_GENERIC = 0x3C,
            AUTH_ERR_FIELD_TOOLONG = 0x3D,
            KDC_ERR_CLIENT_NOT_TRUSTED = 0x3E,
            KDC_ERR_KDC_NOT_TRUSTED = 0x3F,
            KDC_ERR_INVALID_SIG = 0x40,
            KDC_ERR_KEY_TOO_WEAK = 0x41,
            AUTH_AP_ERR_USER_TO_USER_REQUIRED = 0x42,
            AUTH_AP_ERR_NO_TGT = 0x43,
            KDC_ERR_WRONG_REALM = 0x44,
        }
        [Flags]
        public enum DSGETDCNAME_FLAGS : uint
        {
            DS_FORCE_REDISCOVERY = 0x00000001,
            DS_DIRECTORY_SERVICE_REQUIRED = 0x00000010,
            DS_DIRECTORY_SERVICE_PREFERRED = 0x00000020,
            DS_GC_SERVER_REQUIRED = 0x00000040,
            DS_PDC_REQUIRED = 0x00000080,
            DS_BACKGROUND_ONLY = 0x00000100,
            DS_IP_REQUIRED = 0x00000200,
            DS_KDC_REQUIRED = 0x00000400,
            DS_TIMESERV_REQUIRED = 0x00000800,
            DS_WRITABLE_REQUIRED = 0x00001000,
            DS_GOOD_TIMESERV_PREFERRED = 0x00002000,
            DS_AVOID_SELF = 0x00004000,
            DS_ONLY_LDAP_NEEDED = 0x00008000,
            DS_IS_FLAT_NAME = 0x00010000,
            DS_IS_DNS_NAME = 0x00020000,
            DS_RETURN_DNS_NAME = 0x40000000,
            DS_RETURN_FLAT_NAME = 0x80000000
        }
        public enum TOKEN_INFORMATION_CLASS
        {
            TokenUser = 1,
            TokenGroups,
            TokenPrivileges,
            TokenOwner,
            TokenPrimaryGroup,
            TokenDefaultDacl,
            TokenSource,
            TokenType,
            TokenImpersonationLevel,
            TokenStatistics,
            TokenRestrictedSids,
            TokenSessionId,
            TokenGroupsAndPrivileges,
            TokenSessionReference,
            TokenSandBoxInert,
            TokenAuditPolicy,
            TokenOrigin,
            TokenElevationType,
            TokenLinkedToken,
            TokenElevation,
            TokenHasRestrictions,
            TokenAccessInformation,
            TokenVirtualizationAllowed,
            TokenVirtualizationEnabled,
            TokenIntegrityLevel,
            TokenUIAccess,
            TokenMandatoryPolicy,
            TokenLogonSid,
            MaxTokenInfoClass
        }
        [Flags]
        public enum AUTH_CACHE_OPTIONS : UInt64
        {
            AUTH_RETRIEVE_TOKEN_DEFAULT = 0x0,
            AUTH_RETRIEVE_TOKEN_DONT_USE_CACHE = 0x1,
            AUTH_RETRIEVE_TOKEN_USE_CACHE_ONLY = 0x2,
            AUTH_RETRIEVE_TOKEN_USE_CREDHANDLE = 0x4,
            AUTH_RETRIEVE_TOKEN_AS_AUTH_CRED = 0x8,
            AUTH_RETRIEVE_TOKEN_WITH_SEC_CRED = 0x10,
            AUTH_RETRIEVE_TOKEN_CACHE_TOKEN = 0x20,
            AUTH_RETRIEVE_TOKEN_MAX_LIFETIME = 0x40,
        }
        public enum AUTH_PROTOCOL_MESSAGE_TYPE : UInt32
        {
            KerbDebugRequestMessage = 0,
            KerbQueryTokenCacheMessage = 1,
            KerbChangeMachinePasswordMessage = 2,
            KerbVerifyPacMessage = 3,
            KerbRetrieveTokenMessage = 4,
            KerbUpdateAddressesMessage = 5,
            KerbPurgeTokenCacheMessage = 6,
            KerbChangePasswordMessage = 7,
            KerbRetrieveEncodedTokenMessage = 8,
            KerbDecryptDataMessage = 9,
            KerbAddBindingCacheEntryMessage = 10,
            KerbSetPasswordMessage = 11,
            KerbSetPasswordExMessage = 12,
            KerbVerifyCredentialsMessage = 13,
            KerbQueryTokenCacheExMessage = 14,
            KerbPurgeTokenCacheExMessage = 15,
            KerbRefreshSmartcardCredentialsMessage = 16,
            KerbAddExtraCredentialsMessage = 17,
            KerbQuerySupplementalCredentialsMessage = 18,
            KerbTransferCredentialsMessage = 19,
            KerbQueryTokenCacheEx2Message = 20,
            KerbSubmitTokenMessage = 21,
            KerbAddExtraCredentialsExMessage = 22,
            KerbQueryKdcProxyCacheMessage = 23,
            KerbPurgeKdcProxyCacheMessage = 24,
            KerbQueryTokenCacheEx3Message = 25,
            KerbCleanupMachinePkinitCredsMessage = 26,
            KerbAddBindingCacheEntryExMessage = 27,
            KerbQueryBindingCacheMessage = 28,
            KerbPurgeBindingCacheMessage = 29,
            KerbQueryDomainExtendedPoliciesMessage = 30,
            KerbQueryS4U2ProxyCacheMessage = 31
        }
        public enum LogonType : uint
        {
            Interactive = 2,
            Network,
            Batch,
            Service,
            Proxy,
            Unlock,
            NetworkCleartext,
            NewCredentials,
            RemoteInteractive,
            CachedInteractive,
            CachedRemoteInteractive,
            CachedUnlock
        }
        public enum LOGON_PROVIDER
        {
            LOGON32_PROVIDER_DEFAULT,
            LOGON32_PROVIDER_WINNT35,
            LOGON32_PROVIDER_WINNT40,
            LOGON32_PROVIDER_WINNT50
        }
        [Flags]
        public enum ISC_REQ : int
        {
            DELEGATE = 1,
            MUTUAL_AUTH = 2,
            REPLAY_DETECT = 4,
            SEQUENCE_DETECT = 8,
            CONFIDENTIALITY = 16,
            USE_SESSION_KEY = 32,
            PROMPT_FOR_CREDS = 64,
            USE_SUPPLIED_CREDS = 128,
            ALLOCATE_MEMORY = 256,
            USE_DCE_STYLE = 512,
            DATAGRAM = 1024,
            CONNECTION = 2048,
            EXTENDED_ERROR = 16384,
            STREAM = 32768,
            INTEGRITY = 65536,
            MANUAL_CRED_VALIDATION = 524288,
            HTTP = 268435456
        }
        public enum SecBufferType
        {
            SECBUFFER_VERSION = 0,
            SECBUFFER_EMPTY = 0,
            SECBUFFER_DATA = 1,
            SECBUFFER_TOKEN = 2
        }
        public enum HostAddressType : long
        {
            NULL = 0,
            ADDRTYPE_UNIX = 1,
            ADDRTYPE_INET = 2,
            ADDRTYPE_IMPLINK = 3,
            ADDRTYPE_PUP = 4,
            ADDRTYPE_CHAOS = 5,
            ADDRTYPE_XNS = 6,
            ADDRTYPE_IPX = 6,
            ADDRTYPE_OSI = 7,
            ADDRTYPE_ECMA = 8,
            ADDRTYPE_DATAKIT = 9,
            ADDRTYPE_CCITT = 10,
            ADDRTYPE_SNA = 11,
            ADDRTYPE_DECNET = 12,
            ADDRTYPE_DLI = 13,
            ADDRTYPE_LAT = 14,
            ADDRTYPE_HYLINK = 15,
            ADDRTYPE_APPLETALK = 16,
            ADDRTYPE_VOICEVIEW = 18,
            ADDRTYPE_FIREFOX = 19,
            ADDRTYPE_NETBIOS = 20,
            ADDRTYPE_BAN = 21,
            ADDRTYPE_ATM = 22,
            ADDRTYPE_INET6 = 24
        }
        public enum AuthorizationDataType : long
        {
            AD_IF_RELEVANT = 1,
            AD_INTENDED_FOR_SERVER = 2,
            AD_INTENDED_FOR_APPLICATION_CLASS = 3,
            AD_KDCISSUED = 4,
            AD_AND_OR = 5,
            AD_MANDATORY_TOKEN_EXTENSIONS = 6,
            AD_IN_TOKEN_EXTENSIONS = 7,
            AD_MANDATORY_FOR_KDC = 8,
            AD_INITIAL_VERIFIED_CAS = 9,
            OSF_DCE = 64,
            SESAME = 65,
            AD_OSF_DCE_PKI_CERTID = 66,
            AD_CAMMAC = 96,
            AD_AUTHENTICATION_INDICATOR = 97,
            AD_WIN2K_PAC = 128,
            AD_ETYPE_NEGOTIATION = 129,
            AUTH_AUTH_DATA_TOKEN_RESTRICTIONS = 141,
            AUTH_LOCAL = 142,
            AD_AUTH_DATA_AP_OPTIONS = 143,
            AD_TOKEN = 256
        }
        public enum TransitedEncodingType : long
        {
            NULL = 0,
            DOMAIN_X500_COMPRESS = 1
        }
        public enum LSAP_TOKEN_INFO_INTEGRITY_FLAGS : UInt32
        {
            FULL = 0,
            UAC_RESTRICTED = 1
        }
        public enum LSAP_TOKEN_INFO_INTEGRITY_TOKENIL : UInt32
        {
            UNTRUSTED = 0,
            LOW = 4096,
            MEDIUM = 8192,
            HIGH = 12288,
            SYSTEM = 16384,
            PROTECTED = 20480
        }
        public struct LSAP_TOKEN_INFO_INTEGRITY
        {
            public LSAP_TOKEN_INFO_INTEGRITY_FLAGS Flags;
            public LSAP_TOKEN_INFO_INTEGRITY_TOKENIL TokenIL;
            public byte[] machineID;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_ECRYPT
        {
            int Type0;
            public int BlockSize;
            int Type1;
            public int KeySize;
            public int Size;
            int unk2;
            int unk3;
            public IntPtr AlgName;
            public IntPtr Initialize;
            public IntPtr Encrypt;
            public IntPtr Decrypt;
            public IntPtr Finish;
            public IntPtr HashPassword;
            IntPtr RandomKey;
            IntPtr Control;
            IntPtr unk0_null;
            IntPtr unk1_null;
            IntPtr unk2_null;
        }
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        public struct DOMAIN_CONTROLLER_INFO
        {
            [MarshalAs(UnmanagedType.LPTStr)]
            public string DomainControllerName;
            [MarshalAs(UnmanagedType.LPTStr)]
            public string DomainControllerAddress;
            public uint DomainControllerAddressType;
            public Guid DomainGuid;
            [MarshalAs(UnmanagedType.LPTStr)]
            public string DomainName;
            [MarshalAs(UnmanagedType.LPTStr)]
            public string DnsForestName;
            public uint Flags;
            [MarshalAs(UnmanagedType.LPTStr)]
            public string DcSiteName;
            [MarshalAs(UnmanagedType.LPTStr)]
            public string ClientSiteName;
        }
        public struct SYSTEMTIME
        {
            public ushort wYear, wMonth, wDayOfWeek, wDay,
               wHour, wMinute, wSecond, wMilliseconds;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_SUBMIT_TKT_REQUEST
        {
            public AUTH_PROTOCOL_MESSAGE_TYPE MessageType;
            public LUID LogonId;
            public int Flags;
            public AUTH_CRYPTO_KEY32 Key;
            public int KerbCredSize;
            public int KerbCredOffset;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_PURGE_TKT_CACHE_REQUEST
        {
            public AUTH_PROTOCOL_MESSAGE_TYPE MessageType;
            public LUID LogonId;
            LSA_STRING_IN ServerName;
            LSA_STRING_IN RealmName;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_CRYPTO_KEY32
        {
            public int KeyType;
            public int Length;
            public int Offset;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct LSA_STRING_IN
        {
            public UInt16 Length;
            public UInt16 MaximumLength;
            public string Buffer;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct LSA_STRING_OUT
        {
            public UInt16 Length;
            public UInt16 MaximumLength;
            public IntPtr Buffer;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct LSA_STRING
        {
            public UInt16 Length;
            public UInt16 MaximumLength;
            public String Buffer;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct UNICODE_STRING : IDisposable
        {
            public ushort Length;
            public ushort MaximumLength;
            public IntPtr buffer;
            public UNICODE_STRING(string s)
            {
                Length = (ushort)(s.Length * 2);
                MaximumLength = (ushort)(Length + 2);
                buffer = Marshal.StringToHGlobalUni(s);
            }
            public void Dispose()
            {
                Marshal.FreeHGlobal(buffer);
                buffer = IntPtr.Zero;
            }
            public override string ToString()
            {
                return Marshal.PtrToStringUni(buffer);
            }
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_RETRIEVE_TKT_RESPONSE
        {
            public AUTH_EXTERNAL_TOKEN Token;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_EXTERNAL_TOKEN
        {
            public IntPtr ServiceName;
            public IntPtr TargetName;
            public IntPtr ClientName;
            public LSA_STRING_OUT DomainName;
            public LSA_STRING_OUT TargetDomainName;
            public LSA_STRING_OUT AltTargetDomainName;
            public AUTH_CRYPTO_KEY SessionKey;
            public UInt32 TokenFlags;
            public UInt32 Flags;
            public Int64 KeyExpirationTime;
            public Int64 StartTime;
            public Int64 EndTime;
            public Int64 RenewUntil;
            public Int64 TimeSkew;
            public Int32 EncodedTokenSize;
            public IntPtr EncodedToken;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_CRYPTO_KEY
        {
            public Int32 KeyType;
            public Int32 Length;
            public IntPtr Value;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_RETRIEVE_TKT_REQUEST
        {
            public AUTH_PROTOCOL_MESSAGE_TYPE MessageType;
            public LUID LogonId;
            public UNICODE_STRING TargetName;
            public UInt32 TokenFlags;
            public UInt32 CacheOptions;
            public Int32 EncryptionType;
            public SECURITY_HANDLE CredentialsHandle;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_QUERY_TKN_CACHE_REQUEST
        {
            public AUTH_PROTOCOL_MESSAGE_TYPE MessageType;
            public LUID LogonId;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_QUERY_TKN_CACHE_RESPONSE
        {
            public AUTH_PROTOCOL_MESSAGE_TYPE MessageType;
            public int CountOfTokens;
            public IntPtr Tokens;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_TOKEN_CACHE_INFO
        {
            public LSA_STRING_OUT ServerName;
            public LSA_STRING_OUT RealmName;
            public Int64 StartTime;
            public Int64 EndTime;
            public Int64 RenewTime;
            public Int32 EncryptionType;
            public UInt32 TokenFlags;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_TOKEN_CACHE_INFO_EX
        {
            public LSA_STRING_OUT ClientName;
            public LSA_STRING_OUT ClientRealm;
            public LSA_STRING_OUT ServerName;
            public LSA_STRING_OUT ServerRealm;
            public Int64 StartTime;
            public Int64 EndTime;
            public Int64 RenewTime;
            public Int32 EncryptionType;
            public UInt32 TokenFlags;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_TOKEN_CACHE_INFO_EX2
        {
            public LSA_STRING_OUT ClientName;
            public LSA_STRING_OUT ClientRealm;
            public LSA_STRING_OUT ServerName;
            public LSA_STRING_OUT ServerRealm;
            public Int64 StartTime;
            public Int64 EndTime;
            public Int64 RenewTime;
            public Int32 EncryptionType;
            public UInt32 TokenFlags;
            public UInt32 SessionKeyType;
            public UInt32 BranchId;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_TOKEN_CACHE_INFO_EX3
        {
            public LSA_STRING_OUT ClientName;
            public LSA_STRING_OUT ClientRealm;
            public LSA_STRING_OUT ServerName;
            public LSA_STRING_OUT ServerRealm;
            public Int64 StartTime;
            public Int64 EndTime;
            public Int64 RenewTime;
            public Int32 EncryptionType;
            public UInt32 TokenFlags;
            public UInt32 SessionKeyType;
            public UInt32 BranchId;
            public UInt32 CacheFlags;
            public LSA_STRING_OUT KdcCalled;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct AUTH_EXTERNAL_NAME
        {
            public Int16 NameType;
            public UInt16 NameCount;
            [MarshalAs(UnmanagedType.ByValArray,
                SizeConst = 3)]
            public LSA_STRING_OUT[] Names;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct SECURITY_LOGON_SESSION_DATA
        {
            public UInt32 Size;
            public LUID LoginID;
            public LSA_STRING_OUT Username;
            public LSA_STRING_OUT LoginDomain;
            public LSA_STRING_OUT AuthenticationPackage;
            public UInt32 LogonType;
            public UInt32 Session;
            public IntPtr PSiD;
            public UInt64 LoginTime;
            public LSA_STRING_OUT LogonServer;
            public LSA_STRING_OUT DnsDomainName;
            public LSA_STRING_OUT Upn;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct SECURITY_ATTRIBUTES
        {
            public int Length;
            public IntPtr lpSecurityDescriptor;
            public bool bInheritHandle;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct PROCESS_INFORMATION
        {
            public IntPtr hProcess;
            public IntPtr hThread;
            public int dwProcessId;
            public int dwThreadId;
        }
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        public struct STARTUPINFO
        {
            public Int32 cb;
            public string lpReserved;
            public string lpDesktop;
            public string lpTitle;
            public Int32 dwX;
            public Int32 dwY;
            public Int32 dwXSize;
            public Int32 dwYSize;
            public Int32 dwXCountChars;
            public Int32 dwYCountChars;
            public Int32 dwFillAttribute;
            public Int32 dwFlags;
            public Int16 wShowWindow;
            public Int16 cbReserved2;
            public IntPtr lpReserved2;
            public IntPtr hStdInput;
            public IntPtr hStdOutput;
            public IntPtr hStdError;
        }
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        public struct TOKEN_STATISTICS
        {
            public LUID TokenId;
            public LUID AuthenticationId;
            public long ExpirationTime;
            public uint TokenType;
            public uint ImpersonationLevel;
            public uint DynamicCharged;
            public uint DynamicAvailable;
            public uint GroupCount;
            public uint PrivilegeCount;
            public LUID ModifiedId;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct TOKEN_ORIGIN
        {
            public LUID OriginatingLogonSession;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct SecHandle
        {
            IntPtr dwLower;
            IntPtr dwUpper;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct SecBuffer : IDisposable
        {
            public int cbBuffer;
            public int BufferType;
            public IntPtr pvBuffer;
            public SecBuffer(int bufferSize)
            {
                cbBuffer = bufferSize;
                BufferType = (int)SecBufferType.SECBUFFER_TOKEN;
                pvBuffer = Marshal.AllocHGlobal(bufferSize);
            }
            public SecBuffer(byte[] secBufferBytes)
            {
                cbBuffer = secBufferBytes.Length;
                BufferType = (int)SecBufferType.SECBUFFER_TOKEN;
                pvBuffer = Marshal.AllocHGlobal(cbBuffer);
                Marshal.Copy(secBufferBytes, 0, pvBuffer, cbBuffer);
            }
            public SecBuffer(byte[] secBufferBytes, SecBufferType bufferType)
            {
                cbBuffer = secBufferBytes.Length;
                BufferType = (int)bufferType;
                pvBuffer = Marshal.AllocHGlobal(cbBuffer);
                Marshal.Copy(secBufferBytes, 0, pvBuffer, cbBuffer);
            }
            public void Dispose()
            {
                if (pvBuffer != IntPtr.Zero)
                {
                    Marshal.FreeHGlobal(pvBuffer);
                    pvBuffer = IntPtr.Zero;
                }
            }
        }
        public struct MultipleSecBufferHelper
        {
            public byte[] Buffer;
            public SecBufferType BufferType;
            public MultipleSecBufferHelper(byte[] buffer, SecBufferType bufferType)
            {
                if (buffer == null || buffer.Length == 0)
                {
                    throw new ArgumentException("buffer cannot be null or 0 length");
                }
                Buffer = buffer;
                BufferType = bufferType;
            }
        };
        [StructLayout(LayoutKind.Sequential)]
        public struct SecBufferDesc : IDisposable
        {
            public int ulVersion;
            public int cBuffers;
            public IntPtr pBuffers;
            public SecBufferDesc(int bufferSize)
            {
                ulVersion = (int)SecBufferType.SECBUFFER_VERSION;
                cBuffers = 1;
                SecBuffer ThisSecBuffer = new SecBuffer(bufferSize);
                pBuffers = Marshal.AllocHGlobal(Marshal.SizeOf(ThisSecBuffer));
                Marshal.StructureToPtr(ThisSecBuffer, pBuffers, false);
            }
            public SecBufferDesc(byte[] secBufferBytes)
            {
                ulVersion = (int)SecBufferType.SECBUFFER_VERSION;
                cBuffers = 1;
                SecBuffer ThisSecBuffer = new SecBuffer(secBufferBytes);
                pBuffers = Marshal.AllocHGlobal(Marshal.SizeOf(ThisSecBuffer));
                Marshal.StructureToPtr(ThisSecBuffer, pBuffers, false);
            }
            public SecBufferDesc(MultipleSecBufferHelper[] secBufferBytesArray)
            {
                if (secBufferBytesArray == null || secBufferBytesArray.Length == 0)
                {
                    throw new ArgumentException("secBufferBytesArray cannot be null or 0 length");
                }
                ulVersion = (int)SecBufferType.SECBUFFER_VERSION;
                cBuffers = secBufferBytesArray.Length;
                pBuffers = Marshal.AllocHGlobal(Marshal.SizeOf(typeof(SecBuffer)) * cBuffers);
                for (int Index = 0; Index < secBufferBytesArray.Length; Index++)
                {
                    SecBuffer ThisSecBuffer = new SecBuffer(secBufferBytesArray[Index].Buffer, secBufferBytesArray[Index].BufferType);
                    int CurrentOffset = Index * Marshal.SizeOf(typeof(SecBuffer));
                    Marshal.WriteInt32(pBuffers, CurrentOffset, ThisSecBuffer.cbBuffer);
                    Marshal.WriteInt32(pBuffers, CurrentOffset + Marshal.SizeOf(ThisSecBuffer.cbBuffer), ThisSecBuffer.BufferType);
                    Marshal.WriteIntPtr(pBuffers, CurrentOffset + Marshal.SizeOf(ThisSecBuffer.cbBuffer) + Marshal.SizeOf(ThisSecBuffer.BufferType), ThisSecBuffer.pvBuffer);
                }
            }
            public void Dispose()
            {
                if (pBuffers != IntPtr.Zero)
                {
                    if (cBuffers == 1)
                    {
                        SecBuffer ThisSecBuffer = (SecBuffer)Marshal.PtrToStructure(pBuffers, typeof(SecBuffer));
                        ThisSecBuffer.Dispose();
                    }
                    else
                    {
                        for (int Index = 0; Index < cBuffers; Index++)
                        {
                            int CurrentOffset = Index * Marshal.SizeOf(typeof(SecBuffer));
                            IntPtr SecBufferpvBuffer = Marshal.ReadIntPtr(pBuffers, CurrentOffset + Marshal.SizeOf(typeof(int)) + Marshal.SizeOf(typeof(int)));
                            Marshal.FreeHGlobal(SecBufferpvBuffer);
                        }
                    }
                    Marshal.FreeHGlobal(pBuffers);
                    pBuffers = IntPtr.Zero;
                }
            }
            public byte[] GetSecBufferByteArray()
            {
                byte[] Buffer = null;
                if (pBuffers == IntPtr.Zero)
                {
                    throw new InvalidOperationException("Object has already been disposed!!!");
                }
                if (cBuffers == 1)
                {
                    SecBuffer ThisSecBuffer = (SecBuffer)Marshal.PtrToStructure(pBuffers, typeof(SecBuffer));
                    if (ThisSecBuffer.cbBuffer > 0)
                    {
                        Buffer = new byte[ThisSecBuffer.cbBuffer];
                        Marshal.Copy(ThisSecBuffer.pvBuffer, Buffer, 0, ThisSecBuffer.cbBuffer);
                    }
                }
                else
                {
                    int BytesToAllocate = 0;
                    for (int Index = 0; Index < cBuffers; Index++)
                    {
                        int CurrentOffset = Index * Marshal.SizeOf(typeof(SecBuffer));
                        BytesToAllocate += Marshal.ReadInt32(pBuffers, CurrentOffset);
                    }
                    Buffer = new byte[BytesToAllocate];
                    for (int Index = 0, BufferIndex = 0; Index < cBuffers; Index++)
                    {
                        int CurrentOffset = Index * Marshal.SizeOf(typeof(SecBuffer));
                        int BytesToCopy = Marshal.ReadInt32(pBuffers, CurrentOffset);
                        IntPtr SecBufferpvBuffer = Marshal.ReadIntPtr(pBuffers, CurrentOffset + Marshal.SizeOf(typeof(int)) + Marshal.SizeOf(typeof(int)));
                        Marshal.Copy(SecBufferpvBuffer, Buffer, BufferIndex, BytesToCopy);
                        BufferIndex += BytesToCopy;
                    }
                }
                return (Buffer);
            }
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct SECURITY_INTEGER
        {
            public uint LowPart;
            public int HighPart;
            public SECURITY_INTEGER(int dummy)
            {
                LowPart = 0;
                HighPart = 0;
            }
        };
        [StructLayout(LayoutKind.Sequential)]
        public struct SECURITY_HANDLE
        {
            public IntPtr LowPart;
            public IntPtr HighPart;
            public SECURITY_HANDLE(int dummy)
            {
                LowPart = HighPart = IntPtr.Zero;
            }
        };
        [StructLayout(LayoutKind.Sequential)]
        public struct SecPkgContext_Sizes
        {
            public uint cbMaxToken;
            public uint cbMaxSignature;
            public uint cbBlockSize;
            public uint cbSecurityTrailer;
        };
        [DllImport("cryptdll.Dll", CharSet = CharSet.Auto, SetLastError = false)]
        public static extern int CDLocateCSystem(AUTH_ETYPE type, out IntPtr pCheckSum);
        [DllImport("cryptdll.Dll", CharSet = CharSet.Auto, SetLastError = false)]
        public static extern int CDLocateCheckSum(AUTH_CHECKSUM_ALGORITHM type, out IntPtr pCheckSum);
        public delegate int AUTH_ECRYPT_Initialize(byte[] Key, int KeySize, int KeyUsage, out IntPtr pContext);
        public delegate int AUTH_ECRYPT_Encrypt(IntPtr pContext, byte[] data, int dataSize, byte[] output, ref int outputSize);
        public delegate int AUTH_ECRYPT_Decrypt(IntPtr pContext, byte[] data, int dataSize, byte[] output, ref int outputSize);
        public delegate int AUTH_ECRYPT_Finish(ref IntPtr pContext);
        public delegate int AUTH_ECRYPT_HashPassword(UNICODE_STRING Password, UNICODE_STRING Salt, int count, byte[] output);
        public delegate int AUTH_CHECKSUM_Initialize(int unk0, out IntPtr pContext);
        public delegate int AUTH_CHECKSUM_Sum(IntPtr pContext, int Size, byte[] Buffer);
        public delegate int AUTH_CHECKSUM_Finalize(IntPtr pContext, byte[] Buffer);
        public delegate int AUTH_CHECKSUM_Finish(ref IntPtr pContext);
        public delegate int AUTH_CHECKSUM_InitializeEx(byte[] Key, int KeySize, int KeyUsage, out IntPtr pContext);
        [DllImport("Netapi32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern int DsGetDcName(
            [MarshalAs(UnmanagedType.LPTStr)] string ComputerName,
            [MarshalAs(UnmanagedType.LPTStr)] string DomainName,
            [In] int DomainGuid,
            [MarshalAs(UnmanagedType.LPTStr)] string SiteName,
            [MarshalAs(UnmanagedType.U4)] DSGETDCNAME_FLAGS flags,
            out IntPtr pDOMAIN_CONTROLLER_INFO);
        [DllImport("Netapi32.dll", SetLastError = true)]
        public static extern int NetApiBufferFree(IntPtr Buffer);
        [DllImport("kernel32.dll")]
        public extern static void GetSystemTime(ref SYSTEMTIME lpSystemTime);
        [DllImport("secur32.dll", SetLastError = false)]
        public static extern int LsaConnectUntrusted(
            [Out] out IntPtr LsaHandle
        );
        [DllImport("secur32.dll", SetLastError = false)]
        public static extern int LsaLookupAuthenticationPackage(
            [In] IntPtr LsaHandle,
            [In] ref LSA_STRING_IN PackageName,
            [Out] out int AuthenticationPackage
        );
        [DllImport("kernel32.dll")]
        public static extern IntPtr LocalAlloc(
            uint uFlags,
            uint uBytes
        );
        [DllImport("advapi32.dll", SetLastError = true)]
        public static extern uint LsaNtStatusToWinError(
            uint status
        );
        [DllImport("advapi32.dll", SetLastError = true, PreserveSig = true)]
        public static extern uint LsaFreeMemory(
            IntPtr buffer
        );
        [DllImport("kernel32.dll", EntryPoint = "CopyMemory", SetLastError = false)]
        public static extern void CopyMemory(
            IntPtr dest,
            IntPtr src,
            uint count
        );
        [DllImport("secur32.dll", SetLastError = false)]
        public static extern int LsaCallAuthenticationPackage(
            IntPtr LsaHandle,
            int AuthenticationPackage,
            IntPtr ProtocolSubmitBuffer,
            int SubmitBufferLength,
            out IntPtr ProtocolReturnBuffer,
            out int ReturnBufferLength,
            out int ProtocolStatus
        );
        [DllImport("secur32.dll", SetLastError = false)]
        public static extern int LsaDeregisterLogonProcess(
            [In] IntPtr LsaHandle
        );
        [DllImport("secur32.dll", SetLastError = true)]
        public static extern int LsaRegisterLogonProcess(
            LSA_STRING_IN LogonProcessName,
            out IntPtr LsaHandle,
            out ulong SecurityMode
        );
        [DllImport("advapi32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool OpenProcessToken(
            IntPtr ProcessHandle,
            UInt32 DesiredAccess,
            out IntPtr TokenHandle);
        [DllImport("advapi32.dll")]
        public static extern bool DuplicateToken(
            IntPtr ExistingTokenHandle,
            int SECURITY_IMPERSONATION_LEVEL,
            ref IntPtr DuplicateTokenHandle);
        [DllImport("advapi32.dll", SetLastError = true)]
        public static extern bool ImpersonateLoggedOnUser(
            IntPtr hToken);
        [DllImport("advapi32.dll", SetLastError = true)]
        public static extern bool RevertToSelf();
        [DllImport("kernel32.dll")]
        public static extern uint GetLastError();
        [DllImport("advapi32.dll", SetLastError = true)]
        public static extern bool GetTokenInformation(
            IntPtr TokenHandle,
            TOKEN_INFORMATION_CLASS TokenInformationClass,
            IntPtr TokenInformation,
            int TokenInformationLength,
            out int ReturnLength);
        [DllImport("advapi32.dll", SetLastError = true, CharSet = CharSet.Unicode)]
        public static extern bool CreateProcessWithLogonW(
            String userName,
            String domain,
            String password,
            UInt32 logonFlags,
            String applicationName,
            String commandLine,
            UInt32 creationFlags,
            UInt32 environment,
            String currentDirectory,
            ref STARTUPINFO startupInfo,
            out PROCESS_INFORMATION processInformation);
        [DllImport("kernel32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool CloseHandle(
            IntPtr hObject
        );
        [DllImport("Secur32.dll", SetLastError = false)]
        public static extern int LsaEnumerateLogonSessions(
            out UInt64 LogonSessionCount,
            out IntPtr LogonSessionList
        );
        [DllImport("Secur32.dll", SetLastError = false)]
        public static extern uint LsaGetLogonSessionData(
            IntPtr luid,
            out IntPtr ppLogonSessionData
        );
        [DllImport("secur32.dll", SetLastError = false)]
        public static extern uint LsaFreeReturnBuffer(
            IntPtr buffer
        );
        [DllImport("secur32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern int AcquireCredentialsHandle(
            string pszPrincipal,
            string pszPackage,
            int fCredentialUse,
            IntPtr PAuthenticationID,
            IntPtr pAuthData,
            int pGetKeyFn,
            IntPtr pvGetKeyArgument,
            ref SECURITY_HANDLE phCredential,
            ref SECURITY_INTEGER ptsExpiry
        );
        [DllImport("secur32.dll", SetLastError = true)]
        public static extern int InitializeSecurityContext(
            ref SECURITY_HANDLE phCredential,
            IntPtr phContext,
            string pszTargetName,
            int fContextReq,
            int Reserved1,
            int TargetDataRep,
            IntPtr pInput,
            int Reserved2,
            out SECURITY_HANDLE phNewContext,
            out SecBufferDesc pOutput,
            out uint pfContextAttr,
            out SECURITY_INTEGER ptsExpiry
        );
        [DllImport("secur32.dll")]
        public static extern int DeleteSecurityContext(
            ref SECURITY_HANDLE phContext
        );
        [DllImport("secur32.dll", CharSet = CharSet.Auto)]
        public static extern int FreeCredentialsHandle(
            [In] ref SECURITY_HANDLE phCredential
        );
        [DllImport("Secur32.dll")]
        public static extern int FreeContextBuffer(
            ref IntPtr pvContextBuffer
        );
    }
}