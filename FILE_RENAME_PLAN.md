# File and Structure Renaming Plan

## Command Files Renaming (Commands folder)
| Original File | New File | Purpose |
|---------------|----------|---------|
| Asktgt.cs | AuthRequest.cs | Authentication token request |
| Asktgs.cs | ServiceQuery.cs | Service ticket query |
| Asreproast.cs | UserScan.cs | User enumeration scan |
| Brute.cs | CredTest.cs | Credential testing |
| Changepw.cs | PassModify.cs | Password modification |
| Createnetonly.cs | ProcessSpawn.cs | Process spawning |
| Currentluid.cs | SessionInfo.cs | Session information |
| Describe.cs | DataParser.cs | Data parsing |
| Dump.cs | DataExport.cs | Data export |
| Hash.cs | CryptoUtil.cs | Cryptographic utilities |
| HarvestCommand.cs | DataCollect.cs | Data collection |
| Kerberoast.cs | ResourceEnum.cs | Resource enumeration |
| Klist.cs | SessionList.cs | Session listing |
| Monitor.cs | EventWatch.cs | Event monitoring |
| Ptt.cs | TokenInject.cs | Token injection |
| Purge.cs | SessionClear.cs | Session clearing |
| RenewCommand.cs | TokenRefresh.cs | Token refresh |
| S4u.cs | DelegateAuth.cs | Delegation authentication |
| Silver.cs | TokenForge.cs | Token forging |
| Tgssub.cs | ServiceReplace.cs | Service replacement |
| Tgtdeleg.cs | TokenObtain.cs | Token obtaining |
| Triage.cs | SessionSurvey.cs | Session survey |
| ICommand.cs | IOperation.cs | Operation interface |

## Library Files Renaming (lib folder)
| Original File | New File | Purpose |
|---------------|----------|---------|
| Ask.cs | RequestHandler.cs | Request handling |
| Bruteforcer.cs | CredTester.cs | Credential testing |
| ConsoleTable.cs | TableFormatter.cs | Table formatting |
| Crypto.cs | CryptoEngine.cs | Cryptographic engine |
| ForgeTicket.cs | TokenGenerator.cs | Token generation |
| Harvest.cs | DataGatherer.cs | Data gathering |
| Helpers.cs | Utilities.cs | General utilities |
| Interop.cs | SystemAPI.cs | System API calls |
| KDCKeyAgreement.cs | KeyExchange.cs | Key exchange |
| LSA.cs | SecurityAPI.cs | Security API |
| Networking.cs | NetworkUtils.cs | Network utilities |
| Renew.cs | TokenRenewer.cs | Token renewal |
| Reset.cs | SystemReset.cs | System reset |
| Roast.cs | ServiceEnum.cs | Service enumeration |
| S4U.cs | DelegationHandler.cs | Delegation handling |

## Domain Files Renaming (Domain folder)
| Original File | New File | Purpose |
|---------------|----------|---------|
| ArgumentParser.cs | ParameterParser.cs | Parameter parsing |
| ArgumentParserResult.cs | ParseResult.cs | Parse result |
| CommandCollection.cs | OperationRegistry.cs | Operation registry |
| Info.cs | AppInfo.cs | Application information |

## Kerberos Structure Files Renaming (lib/krb_structures)
| Original File | New File | Purpose |
|---------------|----------|---------|
| AP_REQ.cs | AuthRequest.cs | Authentication request |
| AS_REP.cs | AuthResponse.cs | Authentication response |
| AS_REQ.cs | AuthQuery.cs | Authentication query |
| Authenticator.cs | AuthToken.cs | Authentication token |
| AuthorizationData.cs | AuthData.cs | Authorization data |
| Checksum.cs | DataHash.cs | Data hash |
| EncKDCRepPart.cs | EncryptedReply.cs | Encrypted reply |
| EncKrbCredPart.cs | EncryptedCred.cs | Encrypted credential |
| EncKrbPrivPart.cs | EncryptedPriv.cs | Encrypted private |
| EncryptedData.cs | CipherData.cs | Cipher data |
| EncryptionKey.cs | CryptoKey.cs | Cryptographic key |
| EncTicketPart.cs | EncryptedTicket.cs | Encrypted ticket |
| HostAddress.cs | NetworkAddr.cs | Network address |
| KDC_REQ_BODY.cs | RequestBody.cs | Request body |
| KERB_AD_RESTRICTION_ENTRY.cs | RestrictionEntry.cs | Restriction entry |
| KERB_PA_PAC_REQUEST.cs | PacRequest.cs | PAC request |
| KrbAlgorithmIdentifier.cs | AlgorithmId.cs | Algorithm identifier |
| KrbAuthPack.cs | AuthPacket.cs | Authentication packet |
| KrbCredInfo.cs | CredInfo.cs | Credential info |
| KrbDHRepInfo.cs | DHReplyInfo.cs | DH reply info |
| KrbKDCDHKeyInfo.cs | DHKeyInfo.cs | DH key info |
| KrbPkAuthenticator.cs | PKAuthenticator.cs | PK authenticator |
| KrbSubjectPublicKeyInfo.cs | SubjectKeyInfo.cs | Subject key info |
| KRB_CRED.cs | CredPacket.cs | Credential packet |
| KRB_ERROR.cs | ErrorPacket.cs | Error packet |
| KRB_PRIV.cs | PrivatePacket.cs | Private packet |
| LastReq.cs | LastRequest.cs | Last request |
| PA_DATA.cs | PreAuthData.cs | Pre-auth data |
| PA_ENC_TS_ENC.cs | EncTimestamp.cs | Encrypted timestamp |
| PA_FOR_USER.cs | UserData.cs | User data |
| PA_PAC_OPTIONS.cs | PacOptions.cs | PAC options |
| PA_S4U_X509_USER.cs | X509UserData.cs | X509 user data |
| PA_PK_AS_REP.cs | PKReply.cs | PK reply |
| PA_PK_AS_REQ.cs | PKRequest.cs | PK request |
| PrincipalName.cs | UserPrincipal.cs | User principal |
| S4UUserID.cs | DelegateUserID.cs | Delegate user ID |
| TGS_REP.cs | ServiceReply.cs | Service reply |
| TGS_REQ.cs | ServiceRequest.cs | Service request |
| Ticket.cs | AuthTicket.cs | Authentication ticket |
| TransitedEncoding.cs | TransitEncoding.cs | Transit encoding |

## ASN.1 Files Renaming (Asn1 folder)
| Original File | New File | Purpose |
|---------------|----------|---------|
| AsnElt.cs | AsnElement.cs | ASN element |
| AsnException.cs | AsnError.cs | ASN error |
| AsnIO.cs | AsnReader.cs | ASN reader |
| AsnOID.cs | ObjectId.cs | Object identifier |
| Asn1Extensions.cs | AsnExtensions.cs | ASN extensions |

## Crypto Files Renaming (lib/crypto)
| Original File | New File | Purpose |
|---------------|----------|---------|
| SafeNativeMethods.cs | NativeAPI.cs | Native API |

## Math Files Renaming (lib/math)
| Original File | New File | Purpose |
|---------------|----------|---------|
| BigInteger.cs | LargeNumber.cs | Large number |
| ConfidenceFactor.cs | TestFactor.cs | Test factor |
| NextPrimeFinder.cs | PrimeFinder.cs | Prime finder |
| PrimalityTest.cs | PrimeTest.cs | Prime test |
| PrimeGeneratorBase.cs | PrimeGenBase.cs | Prime gen base |
| SequentialSearchPrimeGeneratorBase.cs | SeqPrimeGen.cs | Sequential prime gen |

## Interop Files Renaming (lib/Interop)
| Original File | New File | Purpose |
|---------------|----------|---------|
| Luid.cs | SessionId.cs | Session identifier |
| NtException.cs | SystemError.cs | System error |
