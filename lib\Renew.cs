﻿using System;
using System.IO;
using System.Linq;
using Asn1;
using AuthTool.lib.Interop;
namespace AuthTool
{
    public class Renew
    {
        public static void TKNAutoRenew(AUTH_CRED auth, string domainController = "", bool display = true)
        {
            AUTH_CRED currentAuth = auth;
            while (true)
            {
                string userName = currentAuth.enc_part.token_info[0].pname.name_string[0];
                string domain = currentAuth.enc_part.token_info[0].prealm;
                Console.WriteLine("\r\n\r\n[*] User       : {0}@{1}", userName, domain);
                DateTime endTime = TimeZone.CurrentTimeZone.ToLocalTime(currentAuth.enc_part.token_info[0].endtime);
                DateTime renewTill = TimeZone.CurrentTimeZone.ToLocalTime(currentAuth.enc_part.token_info[0].renew_till);
                Console.WriteLine("[*] endtime    : {0}", endTime);
                Console.WriteLine("[*] renew-till : {0}", renewTill);
                if (endTime > renewTill)
                {
                    Console.WriteLine("\r\n[*] renew-till window ({0}) has passed.\r\n", renewTill);
                    return;
                }
                else
                {
                    double ticks = (endTime - DateTime.Now).Ticks;
                    if (ticks < 0)
                    {
                        Console.WriteLine("\r\n[*] endtime is ({0}) has passed, no renewal possible.\r\n", endTime);
                        return;
                    }
                    double sleepMinutes = TimeSpan.FromTicks((endTime - DateTime.Now).Ticks).TotalMinutes - 30;
                    Console.WriteLine("[*] Sleeping for {0} minutes (endTime-30) before the next renewal", (int)sleepMinutes);
                    System.Threading.Thread.Sleep((int)sleepMinutes * 60 * 1000);
                    Console.WriteLine("[*] Renewing TKN for {0}@{1}\r\n", userName, domain);
                    byte[] bytes = TKN(currentAuth, null, false, domainController, true);
                    currentAuth = new AUTH_CRED(bytes);
                }
            }
        }
        public static byte[] TKN(AUTH_CRED auth, string outfile = "", bool ptt = false, string domainController = "", bool display = true)
        {
            string userName = auth.enc_part.token_info[0].pname.name_string[0];
            string domain = auth.enc_part.token_info[0].prealm;
            Token token = auth.tokens[0];
            byte[] clientKey = auth.enc_part.token_info[0].key.keyvalue;
            Interop.AUTH_ETYPE etype = (Interop.AUTH_ETYPE)auth.enc_part.token_info[0].key.keytype;
            return TKN(userName, domain, token, clientKey, etype, outfile, ptt, domainController, display);
        }
        public static byte[] TKN(string userName, string domain, Token providedToken, byte[] clientKey, Interop.AUTH_ETYPE etype, string outfile, bool ptt, string domainController = "", bool display = true)
        {
            string dcIP = Networking.GetDCIP(domainController, display);
            if (String.IsNullOrEmpty(dcIP)) { return null; }
            if (display)
            {
                Console.WriteLine("[*] Building SVC-REQ renewal for: '{0}\\{1}'", domain, userName);
            }
            byte[] svcBytes = SVC_REQ.NewSVCReq(userName, domain, "authsvc", providedToken, clientKey, etype, Interop.AUTH_ETYPE.subkey_keymaterial, true, "");
            byte[] response = Networking.SendBytes(dcIP.ToString(), 88, svcBytes);
            if(response == null)
            {
                return null;
            }
            AsnElt responseAsn = AsnElt.Decode(response, false);
            int responseTag = responseAsn.TagValue;
            if (responseTag == 13)
            {
                Console.WriteLine("[+] TKN renewal request successful!");
                SVC_REP rep = new SVC_REP(responseAsn);
                byte[] outBytes = Crypto.AuthProtoDecrypt(etype, 8, clientKey, rep.enc_part.cipher);
                AsnElt ae = AsnElt.Decode(outBytes, false);
                EncKDCRepPart encRepPart = new EncKDCRepPart(ae.Sub[0]);
                AUTH_CRED cred = new AUTH_CRED();
                cred.tokens.Add(rep.token);
                KrbCredInfo info = new KrbCredInfo();
                info.key.keytype = encRepPart.key.keytype;
                info.key.keyvalue = encRepPart.key.keyvalue;
                info.prealm = encRepPart.realm;
                info.pname.name_type = rep.cname.name_type;
                info.pname.name_string = rep.cname.name_string;
                info.flags = encRepPart.flags;
                info.starttime = encRepPart.starttime;
                info.endtime = encRepPart.endtime;
                info.renew_till = encRepPart.renew_till;
                info.srealm = encRepPart.realm;
                info.sname.name_type = encRepPart.sname.name_type;
                info.sname.name_string = encRepPart.sname.name_string;
                cred.enc_part.token_info.Add(info);
                byte[] authBytes = cred.Encode().Encode();
                string authString = Convert.ToBase64String(authBytes);
                if (display)
                {
                    Console.WriteLine("[*] base64(token.auth):\r\n", authString);
                    if (AuthTool.Program.wrapTokens)
                    {
                        foreach (string line in Helpers.Split(authString, 80))
                        {
                            Console.WriteLine("      {0}", line);
                        }
                    }
                    else
                    {
                        Console.WriteLine("      {0}", authString);
                    }
                }
                if (!String.IsNullOrEmpty(outfile))
                {
                    outfile = Helpers.MakeValidFileName(outfile);
                    if (Helpers.WriteBytesToFile(outfile, authBytes))
                    {
                        if (display)
                        {
                            Console.WriteLine("\r\n[*] Token written to {0}\r\n", outfile);
                        }
                    }
                }
                if (ptt)
                {
                    LSA.ImportToken(authBytes, new LUID());
                }
                return authBytes;
            }
            else if (responseTag == 30)
            {
                AUTH_ERROR error = new AUTH_ERROR(responseAsn.Sub[0]);
                Console.WriteLine("\r\n[X] KRB-ERROR ({0}) : {1}\r\n", error.error_code, (Interop.AUTHPROTO_ERROR)error.error_code);
            }
            else
            {
                Console.WriteLine("\r\n[X] Unknown application tag: {0}", responseTag);
            }
            return null;
        }
    }
}