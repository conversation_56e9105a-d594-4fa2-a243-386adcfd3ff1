using static AuthTool.Interop;
﻿using System;
using Asn1;
using System.Security.Cryptography.X509Certificates;
using System.Security.Cryptography;
namespace AuthTool {
    public class PA_DATA
    {
        public static readonly Oid Di<PERSON><PERSON><PERSON><PERSON> = new Oid("1.2.840.10046.2.1");
        public PA_DATA()
        {
            type = Interop.PADATA_TYPE.PA_PAC_REQUEST;
            value = new AUTH_PA_PAC_REQUEST();
        }
        public PA_DATA(bool claims, bool branch, bool fullDC, bool rbcd)
        {
            type = Interop.PADATA_TYPE.PA_PAC_OPTIONS;
            value = new PA_PAC_OPTIONS(claims, branch, fullDC, rbcd);
        }
        public PA_DATA(string keyString, Interop.AUTH_ETYPE etype)
        {
            type = Interop.PADATA_TYPE.ENC_TIMESTAMP;
            PA_ENC_TS_ENC temp = new PA_ENC_TS_ENC();
            byte[] rawBytes = temp.Encode().Encode();
            byte[] key = Helpers.StringToByteArray(keyString);
            byte[] encBytes = Crypto.KerberosEncrypt(etype, Interop.AUTH_KEY_USAGE_AUTH_REQ_PA_ENC_TIMESTAMP, key, rawBytes);
            value = new EncryptedData((int)etype, encBytes);
        }
        public PA_DATA(byte[] key, string name, string realm)
        {
            type = Interop.PADATA_TYPE.S4U2SELF;
            value = new PA_FOR_USER(key, name, realm);
        }
        public PA_DATA(byte[] key, string name, string realm, uint nonce)
        {
            type = Interop.PADATA_TYPE.PA_S4U_X509_USER;
            value = new PA_S4U_X509_USER(key, name, realm, nonce);
        }
        public PA_DATA(string crealm, string cname, Token providedToken, byte[] clientKey, Interop.AUTH_ETYPE etype, bool opsec = false, byte[] req_body = null)
        {
            type = Interop.PADATA_TYPE.AP_REQ;
            AP_REQ ap_req = new AP_REQ(crealm, cname, providedToken, clientKey, etype);
            if (opsec)
            {
                var rand = new Random();
                ap_req.authenticator.seq_number = (UInt32)rand.Next(1, Int32.MaxValue);
                Console.WriteLine("[+] Sequence number is: {0}", ap_req.authenticator.seq_number);
                ap_req.authenticator.cusec = rand.Next(0, 999999);
                if (req_body != null)
                    ap_req.authenticator.cksum = new Checksum(Interop.AUTH_CHECKSUM_ALGORITHM.AUTH_CHECKSUM_RSA_MD5, req_body);
            }
            value = ap_req;
        }
        public PA_DATA(X509Certificate2 pkInitCert, KDCKeyAgreement agreement, KDCReqBody kdcRequestBody, bool verifyCerts = false) {
            DateTime now = DateTime.UtcNow;
            KrbPkAuthenticator authenticator = new KrbPkAuthenticator((uint)now.Millisecond, now, now.Millisecond, kdcRequestBody);
            KrbAuthPack authPack = new KrbAuthPack(authenticator, pkInitCert);
            byte[] pubKeyInfo = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] {
                AsnElt.MakeInteger(agreement.P),
                AsnElt.MakeInteger(agreement.G),
            }).Encode();
            authPack.ClientPublicValue = new KrbSubjectPublicKeyInfo(new KrbAlgorithmIdentifier(DiffieHellman, pubKeyInfo),
                AsnElt.MakeInteger(agreement.Y).Encode());
            type = Interop.PADATA_TYPE.PK_AUTH_REQ;
            value = new PA_PK_AUTH_REQ(authPack, pkInitCert, agreement, verifyCerts);
        }
        public PA_DATA(AsnElt body)
        {
            type = (Interop.PADATA_TYPE)body.Sub[0].Sub[0].GetInteger();
            byte[] valueBytes = body.Sub[1].Sub[0].GetOctetString();
            switch (type) {
                case Interop.PADATA_TYPE.PA_PAC_REQUEST:
                    value = new AUTH_PA_PAC_REQUEST(AsnElt.Decode(body.Sub[1].Sub[0].CopyValue()));
                    break;
                case Interop.PADATA_TYPE.PK_AUTH_REP:
                    value = new PA_PK_AUTH_REP(AsnElt.Decode(body.Sub[1].Sub[0].CopyValue()));
                    break;
            }
        }
        public AsnElt Encode()
        {
            AsnElt typeElt = AsnElt.MakeInteger((long)type);
            AsnElt nameTypeSeq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { typeElt });
            nameTypeSeq = AsnElt.MakeImplicit(AsnElt.CONTEXT, 1, nameTypeSeq);
            AsnElt paDataElt;
            if (type == Interop.PADATA_TYPE.PA_PAC_REQUEST)
            {
                paDataElt = ((AUTH_PA_PAC_REQUEST)value).Encode();
                paDataElt = AsnElt.MakeImplicit(AsnElt.CONTEXT, 2, paDataElt);
                AsnElt seq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { nameTypeSeq, paDataElt });
                return seq;
            }
            else if (type == Interop.PADATA_TYPE.ENC_TIMESTAMP)
            {
                AsnElt blob = AsnElt.MakeBlob(((EncryptedData)value).Encode().Encode());
                AsnElt blobSeq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { blob });
                blobSeq = AsnElt.MakeImplicit(AsnElt.CONTEXT, 2, blobSeq);
                AsnElt seq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { nameTypeSeq, blobSeq });
                return seq;
            }
            else if (type == Interop.PADATA_TYPE.AP_REQ)
            {
                AsnElt blob = AsnElt.MakeBlob(((AP_REQ)value).Encode().Encode());
                AsnElt blobSeq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { blob });
                paDataElt = AsnElt.MakeImplicit(AsnElt.CONTEXT, 2, blobSeq);
                AsnElt seq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { nameTypeSeq, paDataElt });
                return seq;
            }
            else if (type == Interop.PADATA_TYPE.S4U2SELF)
            {
                AsnElt blob = AsnElt.MakeBlob(((PA_FOR_USER)value).Encode().Encode());
                AsnElt blobSeq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { blob });
                paDataElt = AsnElt.MakeImplicit(AsnElt.CONTEXT, 2, blobSeq);
                AsnElt seq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { nameTypeSeq, paDataElt });
                return seq;
            }
            else if (type == Interop.PADATA_TYPE.PA_S4U_X509_USER)
            {
                AsnElt blob = AsnElt.MakeBlob(((PA_S4U_X509_USER)value).Encode().Encode());
                AsnElt blobSeq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { blob });
                paDataElt = AsnElt.MakeImplicit(AsnElt.CONTEXT, 2, blobSeq);
                AsnElt seq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { nameTypeSeq, paDataElt });
                return seq;
            }
            else if (type == Interop.PADATA_TYPE.PA_PAC_OPTIONS)
            {
                AsnElt blob = AsnElt.MakeBlob(((PA_PAC_OPTIONS)value).Encode().Encode());
                AsnElt blobSeq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { blob });
                paDataElt = AsnElt.MakeImplicit(AsnElt.CONTEXT, 2, blobSeq);
                AsnElt seq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { nameTypeSeq, paDataElt });
                return seq;
            }
            else if(type == Interop.PADATA_TYPE.PK_AUTH_REQ) {
                AsnElt blob = AsnElt.MakeBlob(((PA_PK_AUTH_REQ)value).Encode().Encode());
                AsnElt blobSeq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { blob });
                paDataElt = AsnElt.MakeImplicit(AsnElt.CONTEXT, 2, blobSeq);
                AsnElt seq = AsnElt.Make(AsnElt.SEQUENCE, new AsnElt[] { nameTypeSeq, paDataElt });
                return seq;
            }
            else
            {
                return null;
            }
        }
        public Interop.PADATA_TYPE type { get; set; }
        public Object value { get; set; }
    }
}