﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.ComponentModel;
namespace AuthTool
{
    public class Crypto
    {
        public static void ComputeAllKerberosPasswordHashes(string password, string userName = "", string domainName = "")
        {
            Console.WriteLine("[*] Input password             : {0}", password);
            string salt = String.Format("{0}{1}", domainName.ToUpper(), userName.ToLower());
            if (userName.EndsWith("$"))
            {
                salt = String.Format("{0}host{1}.{2}", domainName.ToUpper(), userName.TrimEnd('$').ToLower(), domainName.ToLower());
            }
            if (!String.IsNullOrEmpty(userName) && !String.IsNullOrEmpty(domainName))
            {
                Console.WriteLine("[*] Input username             : {0}", userName);
                Console.WriteLine("[*] Input domain               : {0}", domainName);
                Console.WriteLine("[*] Salt                       : {0}", salt);
            }
            string rc4Hash = KerberosPasswordHash(Interop.AUTH_ETYPE.rc4_hmac, password);
            Console.WriteLine("[*]       rc4_hmac             : {0}", rc4Hash);
            if (String.IsNullOrEmpty(userName) || String.IsNullOrEmpty(domainName))
            {
                Console.WriteLine("\r\n[!] /user:X and /domain:Y need to be supplied to calculate AES and DES hash types!");
            }
            else
            {
                string aes128Hash = KerberosPasswordHash(Interop.AUTH_ETYPE.aes128_cts_hmac_sha1, password, salt);
                Console.WriteLine("[*]       aes128_cts_hmac_sha1 : {0}", aes128Hash);
                string aes256Hash = KerberosPasswordHash(Interop.AUTH_ETYPE.aes256_cts_hmac_sha1, password, salt);
                Console.WriteLine("[*]       aes256_cts_hmac_sha1 : {0}", aes256Hash);
                string desHash = KerberosPasswordHash(Interop.AUTH_ETYPE.des_cbc_md5, String.Format("{0}{1}", password, salt), salt);
                Console.WriteLine("[*]       des_cbc_md5          : {0}", desHash);
            }
            Console.WriteLine();
        }
        public static string KerberosPasswordHash(Interop.AUTH_ETYPE etype, string password, string salt = "", int count = 4096)
        {
            Interop.AUTH_ECRYPT pCSystem;
            IntPtr pCSystemPtr;
            int status = Interop.CDLocateCSystem(etype, out pCSystemPtr);
            pCSystem = (Interop.AUTH_ECRYPT)System.Runtime.InteropServices.Marshal.PtrToStructure(pCSystemPtr, typeof(Interop.AUTH_ECRYPT));
            if (status != 0)
                throw new System.ComponentModel.Win32Exception(status, "Error on CDLocateCSystem");
            Interop.AUTH_ECRYPT_HashPassword pCSystemHashPassword = (Interop.AUTH_ECRYPT_HashPassword)System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer(pCSystem.HashPassword, typeof(Interop.AUTH_ECRYPT_HashPassword));
            Interop.UNICODE_STRING passwordUnicode = new Interop.UNICODE_STRING(password);
            Interop.UNICODE_STRING saltUnicode = new Interop.UNICODE_STRING(salt);
            byte[] output = new byte[pCSystem.KeySize];
            int success = pCSystemHashPassword(passwordUnicode, saltUnicode, count, output);
            if (status != 0)
                throw new Win32Exception(status);
            return System.BitConverter.ToString(output).Replace("-", "");
        }
        public static byte[] KerberosChecksum(byte[] key, byte[] data, Interop.AUTH_CHECKSUM_ALGORITHM cksumType = Interop.AUTH_CHECKSUM_ALGORITHM.AUTH_CHECKSUM_HMAC_MD5)
        {
            Interop.AUTH_CHECKSUM pCheckSum;
            IntPtr pCheckSumPtr;
            int status = Interop.CDLocateCheckSum(cksumType, out pCheckSumPtr);
            pCheckSum = (Interop.AUTH_CHECKSUM)Marshal.PtrToStructure(pCheckSumPtr, typeof(Interop.AUTH_CHECKSUM));
            if (status != 0)
            {
                throw new Win32Exception(status, "CDLocateCheckSum failed");
            }
            IntPtr Context;
            Interop.AUTH_CHECKSUM_InitializeEx pCheckSumInitializeEx = (Interop.AUTH_CHECKSUM_InitializeEx)Marshal.GetDelegateForFunctionPointer(pCheckSum.InitializeEx, typeof(Interop.AUTH_CHECKSUM_InitializeEx));
            Interop.AUTH_CHECKSUM_Sum pCheckSumSum = (Interop.AUTH_CHECKSUM_Sum)Marshal.GetDelegateForFunctionPointer(pCheckSum.Sum, typeof(Interop.AUTH_CHECKSUM_Sum));
            Interop.AUTH_CHECKSUM_Finalize pCheckSumFinalize = (Interop.AUTH_CHECKSUM_Finalize)Marshal.GetDelegateForFunctionPointer(pCheckSum.Finalize, typeof(Interop.AUTH_CHECKSUM_Finalize));
            Interop.AUTH_CHECKSUM_Finish pCheckSumFinish = (Interop.AUTH_CHECKSUM_Finish)Marshal.GetDelegateForFunctionPointer(pCheckSum.Finish, typeof(Interop.AUTH_CHECKSUM_Finish));
            int status2 = pCheckSumInitializeEx(key, key.Length, 17, out Context);
            if (status2 != 0)
                throw new Win32Exception(status2);
            byte[] checksumSrv = new byte[pCheckSum.Size];
            pCheckSumSum(Context, data.Length, data);
            pCheckSumFinalize(Context, checksumSrv);
            pCheckSumFinish(ref Context);
            return checksumSrv;
        }
        public static byte[] KerberosDecrypt(Interop.AUTH_ETYPE eType, int keyUsage, byte[] key, byte[] data)
        {
            Interop.AUTH_ECRYPT pCSystem;
            IntPtr pCSystemPtr;
            int status = Interop.CDLocateCSystem(eType, out pCSystemPtr);
            pCSystem = (Interop.AUTH_ECRYPT)Marshal.PtrToStructure(pCSystemPtr, typeof(Interop.AUTH_ECRYPT));
            if (status != 0)
                throw new Win32Exception(status, "Error on CDLocateCSystem");
            IntPtr pContext;
            Interop.AUTH_ECRYPT_Initialize pCSystemInitialize = (Interop.AUTH_ECRYPT_Initialize)Marshal.GetDelegateForFunctionPointer(pCSystem.Initialize, typeof(Interop.AUTH_ECRYPT_Initialize));
            Interop.AUTH_ECRYPT_Decrypt pCSystemDecrypt = (Interop.AUTH_ECRYPT_Decrypt)Marshal.GetDelegateForFunctionPointer(pCSystem.Decrypt, typeof(Interop.AUTH_ECRYPT_Decrypt));
            Interop.AUTH_ECRYPT_Finish pCSystemFinish = (Interop.AUTH_ECRYPT_Finish)Marshal.GetDelegateForFunctionPointer(pCSystem.Finish, typeof(Interop.AUTH_ECRYPT_Finish));
            status = pCSystemInitialize(key, key.Length, keyUsage, out pContext);
            if (status != 0)
                throw new Win32Exception(status);
            int outputSize = data.Length;
            if (data.Length % pCSystem.BlockSize != 0)
                outputSize += pCSystem.BlockSize - (data.Length % pCSystem.BlockSize);
            string algName = Marshal.PtrToStringAuto(pCSystem.AlgName);
            outputSize += pCSystem.Size;
            byte[] output = new byte[outputSize];
            status = pCSystemDecrypt(pContext, data, data.Length, output, ref outputSize);
            pCSystemFinish(ref pContext);
            return output;
        }
        public static byte[] KerberosEncrypt(Interop.AUTH_ETYPE eType, int keyUsage, byte[] key, byte[] data)
        {
            Interop.AUTH_ECRYPT pCSystem;
            IntPtr pCSystemPtr;
            int status = Interop.CDLocateCSystem(eType, out pCSystemPtr);
            pCSystem = (Interop.AUTH_ECRYPT)Marshal.PtrToStructure(pCSystemPtr, typeof(Interop.AUTH_ECRYPT));
            if (status != 0)
                throw new Win32Exception(status, "Error on CDLocateCSystem");
            IntPtr pContext;
            Interop.AUTH_ECRYPT_Initialize pCSystemInitialize = (Interop.AUTH_ECRYPT_Initialize)Marshal.GetDelegateForFunctionPointer(pCSystem.Initialize, typeof(Interop.AUTH_ECRYPT_Initialize));
            Interop.AUTH_ECRYPT_Encrypt pCSystemEncrypt = (Interop.AUTH_ECRYPT_Encrypt)Marshal.GetDelegateForFunctionPointer(pCSystem.Encrypt, typeof(Interop.AUTH_ECRYPT_Encrypt));
            Interop.AUTH_ECRYPT_Finish pCSystemFinish = (Interop.AUTH_ECRYPT_Finish)Marshal.GetDelegateForFunctionPointer(pCSystem.Finish, typeof(Interop.AUTH_ECRYPT_Finish));
            status = pCSystemInitialize(key, key.Length, keyUsage, out pContext);
            if (status != 0)
                throw new Win32Exception(status);
            int outputSize = data.Length;
            if (data.Length % pCSystem.BlockSize != 0)
                outputSize += pCSystem.BlockSize - (data.Length % pCSystem.BlockSize);
            string algName = Marshal.PtrToStringAuto(pCSystem.AlgName);
            outputSize += pCSystem.Size;
            byte[] output = new byte[outputSize];
            status = pCSystemEncrypt(pContext, data, data.Length, output, ref outputSize);
            pCSystemFinish(ref pContext);
            return output;
        }
    }
}