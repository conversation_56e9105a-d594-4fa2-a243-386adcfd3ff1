#!/usr/bin/env python3
import os
import re
import glob

def replace_ticket_terms(file_path):
    """Replace all ticket-related terms with generic authentication terms"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        try:
            with open(file_path, 'r', encoding='utf-8-sig') as f:
                content = f.read()
        except:
            with open(file_path, 'r', encoding='latin-1') as f:
                content = f.read()
    
    # Define replacement mappings (case-sensitive)
    replacements = {
        # Core ticket terms
        'ticket': 'token',
        'Ticket': 'Token',
        'TICKET': 'TOKEN',
        'tickets': 'tokens',
        'Tickets': 'Tokens',
        'TICKETS': 'TOKENS',
        
        # Kerberos specific terms
        'TGT': 'TKN',
        'tgt': 'tkn',
        'TGS': 'SVC',
        'tgs': 'svc',
        'TGS_REQ': 'SVC_REQ',
        'TGS_REP': 'SVC_REP',
        'AS_REQ': 'AUTH_REQ',
        'AS_REP': 'AUTH_REP',
        
        # File extensions and formats
        '.kirbi': '.auth',
        'kirbi': 'auth',
        'KIRBI': 'AUTH',
        
        # Function and variable names
        'ticketBytes': 'tokenBytes',
        'ticketData': 'tokenData',
        'ticketInfo': 'tokenInfo',
        'ticket_info': 'token_info',
        'ticketFile': 'tokenFile',
        'ticketFilename': 'tokenFilename',
        'ticketB64': 'tokenB64',
        'ticketname': 'tokenname',
        'saveTicket': 'saveToken',
        'saveTickets': 'saveTokens',
        'SaveTickets': 'SaveTokens',
        'ParseSaveTickets': 'ParseSaveTokens',
        'HandleTicket': 'HandleToken',
        'PrintTicketBase64': 'PrintTokenBase64',
        'wrapTickets': 'wrapTokens',
        'noticket': 'notoken',
        '/noticket': '/notoken',
        'HarvestTicketGrantingTokens': 'HarvestTokenGrantingTokens',
        'EnumerateTokens': 'EnumerateTokens',
        'DisplayToken': 'DisplayToken',
        'TokenDisplayFormat': 'TokenDisplayFormat',
        'ForgeTicket': 'ForgeToken',
        'ImportTicket': 'ImportToken',
        'RequestFakeDelegTicket': 'RequestFakeDelegToken',
        'harvesterTicketCache': 'harvesterTokenCache',
        'renewTickets': 'renewTokens',
        'currentTickets': 'currentTokens',
        'AddTicketsToTicketCache': 'AddTokensToTokenCache',
        'RefreshTokenCache': 'RefreshTokenCache',
        'TokenCache': 'TokenCache',
        'tokenCache': 'tokenCache',
        'SaveTicketsToRegistry': 'SaveTokensToRegistry',
        'newTicketsAdded': 'newTokensAdded',
        'ticketInCache': 'tokenInCache',
        'cachedTicket': 'cachedToken',
        'displayNewTickets': 'displayNewTokens',
        'newTickets': 'newTokens',
        'TicketFlags': 'TokenFlags',
        'ticketFlags': 'tokenFlags',
        'additional_tickets': 'additional_tokens',
        'PrintTicket': 'PrintToken',
        'encTicketData': 'encTokenData',
        'decTicketPart': 'decTokenPart',
        'ticketASN': 'tokenASN',
        'ticketAsn': 'tokenAsn',
        'ticketSeq': 'tokenSeq',
        'ticktSeq': 'tokenSeq',
        'providedTicket': 'providedToken',
        'ticketSeq2': 'tokenSeq2',
        'AUTH_TICKET': 'AUTH_TOKEN',
        'ExtractTicket': 'ExtractToken',
        'ticketKirbi': 'tokenKirbi',
        'KerbRetrieveEncodedTicketMessage': 'KerbRetrieveEncodedTokenMessage',
        'encodedTicketSize': 'encodedTokenSize',
        'encodedTicket': 'encodedToken',
        'EncodedTicketSize': 'EncodedTokenSize',
        'EncodedTicket': 'EncodedToken',
        'extractTicketData': 'extractTokenData',
        'ticketsPointer': 'tokensPointer',
        'ticketCacheRequest': 'tokenCacheRequest',
        'ticketCacheResponse': 'tokenCacheResponse',
        'ticketCacheResult': 'tokenCacheResult',
        'KerbQueryTicketCacheExMessage': 'KerbQueryTokenCacheExMessage',
        'AUTH_QUERY_TKT_CACHE_REQUEST': 'AUTH_QUERY_TKN_CACHE_REQUEST',
        'AUTH_QUERY_TKT_CACHE_RESPONSE': 'AUTH_QUERY_TKN_CACHE_RESPONSE',
        'CountOfTickets': 'CountOfTokens',
        'currTicketPtr': 'currTokenPtr',
        'includeTicket': 'includeToken',
        'displayB64ticket': 'displayB64token',
        'base64ticket': 'base64token',
        'Base64EncodedTicket': 'Base64EncodedToken',
        'KerbSubmitTicketMessage': 'KerbSubmitTokenMessage',
        'KerbPurgeTicketCacheMessage': 'KerbPurgeTokenCacheMessage',
        'AUTH_RETRIEVE_TICKET_USE_CACHE_ONLY': 'AUTH_RETRIEVE_TOKEN_USE_CACHE_ONLY',
        'ticketDomain': 'tokenDomain',
        'AUTH_RETRIEVE_TICKET_DEFAULT': 'AUTH_RETRIEVE_TOKEN_DEFAULT',
        'AUTH_RETRIEVE_TICKET_DONT_USE_CACHE': 'AUTH_RETRIEVE_TOKEN_DONT_USE_CACHE',
        'AUTH_RETRIEVE_TICKET_USE_CREDHANDLE': 'AUTH_RETRIEVE_TOKEN_USE_CREDHANDLE',
        'AUTH_RETRIEVE_TICKET_AS_AUTH_CRED': 'AUTH_RETRIEVE_TOKEN_AS_AUTH_CRED',
        'AUTH_RETRIEVE_TICKET_WITH_SEC_CRED': 'AUTH_RETRIEVE_TOKEN_WITH_SEC_CRED',
        'AUTH_RETRIEVE_TICKET_CACHE_TICKET': 'AUTH_RETRIEVE_TOKEN_CACHE_TOKEN',
        'AUTH_RETRIEVE_TICKET_MAX_LIFETIME': 'AUTH_RETRIEVE_TOKEN_MAX_LIFETIME',
        'KerbQueryTicketCacheMessage': 'KerbQueryTokenCacheMessage',
        'KerbRetrieveTicketMessage': 'KerbRetrieveTokenMessage',
        'KerbPurgeTicketCacheExMessage': 'KerbPurgeTokenCacheExMessage',
        'KerbQueryTicketCacheEx2Message': 'KerbQueryTokenCacheEx2Message',
        'KerbQueryTicketCacheEx3Message': 'KerbQueryTokenCacheEx3Message',
        'AD_MANDATORY_TICKET_EXTENSIONS': 'AD_MANDATORY_TOKEN_EXTENSIONS',
        'AD_IN_TICKET_EXTENSIONS': 'AD_IN_TOKEN_EXTENSIONS',
        'AUTH_EXTERNAL_TICKET': 'AUTH_EXTERNAL_TOKEN',
        
        # Class and structure names
        'EncTicketPart': 'EncTokenPart',
        'ticketPart': 'tokenPart',
        'encTicketPart': 'encTokenPart',
        
        # Messages and strings
        'apply a ticket': 'apply a token',
        'Saved TGT': 'Saved TKN',
        'ticket to': 'token to',
        'ticket from': 'token from',
        'ticket for': 'token for',
        'ticket in': 'token in',
        'ticket with': 'token with',
        'ticket into': 'token into',
        'ticket cache': 'token cache',
        'ticket granting': 'token granting',
        'service ticket': 'service token',
        'Service ticket': 'Service token',
        'SERVICE TICKET': 'SERVICE TOKEN',
        
        # Protocol specific
        'krbtgt': 'authsvc',
        'KRBTGT': 'AUTHSVC',
        'KRB_': 'AUTH_',
        'KERB_': 'AUTH_',
        
        # Additional terms
        'kerberos': 'authproto',
        'Kerberos': 'AuthProto',
        'KERBEROS': 'AUTHPROTO',
    }
    
    # Apply replacements
    original_content = content
    for old_term, new_term in replacements.items():
        # Use word boundaries for whole word replacement where appropriate
        if old_term.isalpha():
            # For alphabetic terms, use word boundaries
            pattern = r'\b' + re.escape(old_term) + r'\b'
            content = re.sub(pattern, new_term, content)
        else:
            # For terms with special characters, do direct replacement
            content = content.replace(old_term, new_term)
    
    # Only write if content changed
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    return False

def main():
    # Find all C# files
    cs_files = glob.glob('**/*.cs', recursive=True)
    
    print(f"Found {len(cs_files)} C# files to process...")
    
    processed = 0
    changed = 0
    for file_path in cs_files:
        try:
            if replace_ticket_terms(file_path):
                changed += 1
            processed += 1
            if processed % 20 == 0:
                print(f"Processed {processed}/{len(cs_files)} files... ({changed} changed)")
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
    
    print(f"Successfully processed {processed} files!")
    print(f"Changed {changed} files with ticket-related terms.")

if __name__ == "__main__":
    main()
