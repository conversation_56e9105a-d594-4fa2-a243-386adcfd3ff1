using static AuthTool.Interop;
﻿using Asn1;
using System;
using System.Text;
namespace AuthTool
{
    public class SVC_REP
    {
        public SVC_REP(byte[] data)
        {
            AsnElt asn_SVC_REP = AsnElt.Decode(data, false);
            this.Decode(asn_SVC_REP);
        }
        public SVC_REP(AsnElt asn_SVC_REP)
        {
            this.Decode(asn_SVC_REP);
        }
        private void Decode(AsnElt asn_SVC_REP)
        {
            if (asn_SVC_REP.TagValue != (int)Interop.AUTH_MESSAGE_TYPE.SVC_REP)
            {
                throw new System.Exception("SVC-REP tag value should be 13");
            }
            if ((asn_SVC_REP.Sub.Length != 1) || (asn_SVC_REP.Sub[0].TagValue != 16))
            {
                throw new System.Exception("First SVC-REP sub should be a sequence");
            }
            AsnElt[] kdc_rep = asn_SVC_REP.Sub[0].Sub;
            foreach (AsnElt s in kdc_rep)
            {
                switch (s.TagValue)
                {
                    case 0:
                        pvno = s.Sub[0].GetInteger();
                        break;
                    case 1:
                        msg_type = s.Sub[0].GetInteger();
                        break;
                    case 2:
                        padata = new PA_DATA(s.Sub[0]);
                        break;
                    case 3:
                        crealm = Encoding.ASCII.GetString(s.Sub[0].GetOctetString());
                        break;
                    case 4:
                        cname = new PrincipalName(s.Sub[0]);
                        break;
                    case 5:
                        token = new Token(s.Sub[0].Sub[0]);
                        break;
                    case 6:
                        enc_part = new EncryptedData(s.Sub[0]);
                        break;
                    default:
                        break;
                }
            }
        }
        public long pvno { get; set; }
        public long msg_type { get; set; }
        public PA_DATA padata { get; set; }
        public string crealm { get; set; }
        public PrincipalName cname { get; set; }
        public Token token { get; set; }
        public EncryptedData enc_part { get; set; }
    }
}