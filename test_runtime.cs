using System;

class Program
{
    static void Main()
    {
        Console.WriteLine("Runtime Version: " + Environment.Version);
        Console.WriteLine("Framework Version: " + System.Runtime.InteropServices.RuntimeEnvironment.GetSystemVersion());
        Console.WriteLine("OS Version: " + Environment.OSVersion);
        Console.WriteLine("64-bit Process: " + Environment.Is64BitProcess);
        Console.WriteLine("64-bit OS: " + Environment.Is64BitOperatingSystem);
        Console.ReadKey();
    }
}
