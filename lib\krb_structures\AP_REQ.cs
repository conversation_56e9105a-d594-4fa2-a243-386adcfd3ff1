﻿using Asn1;
using System;
using System.Collections.Generic;
using System.IO;
using static AuthTool.Interop;
namespace AuthTool
{
    public class AP_REQ
    {
        public AP_REQ(string crealm, string cname, Token providedToken, byte[] clientKey, Interop.AUTH_ETYPE etype, int keyUsageSpec = Interop.AUTH_KEY_USAGE_SVC_REQ_PA_AUTHENTICATOR)
        {
            pvno = 5;
            msg_type = (long)Interop.AUTH_MESSAGE_TYPE.AP_REQ;
            ap_options = 0;
            token = providedToken;
            keyUsage = keyUsageSpec;
            enctype = etype;
            key = clientKey;
            authenticator = new Authenticator();
            authenticator.crealm = crealm;
            authenticator.cname = new PrincipalName(cname);
        }
        public AsnElt Encode()
        {
            AsnElt pvnoASN = AsnElt.MakeInteger(pvno);
            AsnElt pvnoSeq = AsnElt.Make(AsnElt.SEQUENCE, new[] { pvnoASN });
            pvnoSeq = AsnElt.MakeImplicit(AsnElt.CONTEXT, 0, pvnoSeq);
            AsnElt msg_typeASN = AsnElt.MakeInteger(msg_type);
            AsnElt msg_typeSeq = AsnElt.Make(AsnElt.SEQUENCE, new[] { msg_typeASN });
            msg_typeSeq = AsnElt.MakeImplicit(AsnElt.CONTEXT, 1, msg_typeSeq);
            byte[] ap_optionsBytes = BitConverter.GetBytes(ap_options);
            AsnElt ap_optionsASN = AsnElt.MakeBitString(ap_optionsBytes);
            AsnElt ap_optionsSeq = AsnElt.Make(AsnElt.SEQUENCE, new[] { ap_optionsASN });
            ap_optionsSeq = AsnElt.MakeImplicit(AsnElt.CONTEXT, 2, ap_optionsSeq);
            AsnElt tokenASN = token.Encode();
            AsnElt tokenSeq = AsnElt.Make(AsnElt.SEQUENCE, new[] { tokenASN });
            tokenSeq = AsnElt.MakeImplicit(AsnElt.CONTEXT, 3, tokenSeq);
            if (key == null)
            {
                Console.WriteLine("  [X] A key for the authenticator is needed to build an AP-REQ");
                return null;
            }
            byte[] authenticatorBytes = authenticator.Encode().Encode();
            byte[] encBytes = Crypto.KerberosEncrypt(enctype, keyUsage, key, authenticatorBytes);
            EncryptedData authenticatorEncryptedData = new EncryptedData();
            authenticatorEncryptedData.etype = (int)enctype;
            authenticatorEncryptedData.cipher = encBytes;
            AsnElt authenticatorEncryptedDataASN = authenticatorEncryptedData.Encode();
            AsnElt authenticatorEncryptedDataSeq = AsnElt.Make(AsnElt.SEQUENCE, new[] { authenticatorEncryptedDataASN });
            authenticatorEncryptedDataSeq = AsnElt.MakeImplicit(AsnElt.CONTEXT, 4, authenticatorEncryptedDataSeq);
            AsnElt[] total = new[] { pvnoSeq, msg_typeSeq, ap_optionsSeq, tokenSeq, authenticatorEncryptedDataSeq };
            AsnElt seq = AsnElt.Make(AsnElt.SEQUENCE, total);
            AsnElt totalSeq = AsnElt.Make(AsnElt.SEQUENCE, new[] { seq });
            totalSeq = AsnElt.MakeImplicit(AsnElt.APPLICATION, 14, totalSeq);
            return totalSeq;
        }
        public long pvno { get; set;}
        public long msg_type { get; set; }
        public UInt32 ap_options { get; set; }
        public Token token { get; set; }
        public Authenticator authenticator { get; set; }
        public byte[] key { get; set; }
        private Interop.AUTH_ETYPE enctype;
        private int keyUsage;
    }
}