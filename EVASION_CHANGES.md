# Rubeus Evasion Modifications

## Summary of Changes Made

### 1. String Obfuscation System
- **Created**: `lib/StringObfuscator.cs` - Utility class for XOR-based string obfuscation
- **Features**: XOR encoding, ROT13, character substitution, chunk reversal
- **Usage**: `StringObfuscator.GetString("obfuscated_base64_string")`

### 2. Project and Assembly Renaming
- **Project Name**: `<PERSON><PERSON>` → `AuthTool`
- **Assembly Name**: `<PERSON><PERSON>` → `AuthTool`
- **Namespace**: `<PERSON><PERSON>` → `AuthTool`
- **Assembly Title**: `<PERSON><PERSON>` → `AuthTool`
- **Assembly Product**: `<PERSON><PERSON>` → `AuthTool`
- **Copyright Year**: 2018 → 2024
- **Company**: Empty → `Enterprise Solutions`

### 3. Logo and Banner Obfuscation
- **Removed**: ASCII art "Rubeus" logo
- **Replaced**: Generic "Authentication Tool v2.0.0" banner
- **Obfuscated**: All banner text using StringObfuscator

### 4. Usage Text Obfuscation
- **Removed**: Detailed help text with "Ruben.exe" references
- **Replaced**: Short, generic usage instructions
- **Obfuscated**: All usage text using StringObfuscator

### 5. Command Name Changes
- `kerberoast` → `enumerate`
- `asktgt` → `request`
- `currentluid` → `currentid`

### 6. Console Output Obfuscation
- **Obfuscated**: Error messages and action descriptions
- **Example**: "[*] Action: Kerberoasting" → obfuscated equivalent

## Files Modified

### Core Files
- `Program.cs` - Updated namespace, obfuscated exception messages
- `Domain/Info.cs` - New banner, obfuscated usage text
- `Domain/CommandCollection.cs` - Updated namespace
- `Properties/AssemblyInfo.cs` - Changed assembly metadata

### Command Files (Partially Updated)
- `Commands/Kerberoast.cs` - Renamed to "enumerate", obfuscated output
- `Commands/Asktgt.cs` - Renamed to "request", obfuscated output  
- `Commands/Currentluid.cs` - Renamed to "currentid", obfuscated output

### Project Files
- `Ruben.csproj` - Updated assembly name and namespace
- Added `lib/StringObfuscator.cs` to compilation

## Remaining Tasks

### 1. Complete Command Renaming
Update remaining commands with generic names:
- `asktgs` → `extract`
- `asreproast` → `scan`
- `brute` → `test`
- `changepw` → `modify`
- `createnetonly` → `spawn`
- `describe` → `info`
- `dump` → `export`
- `hash` → `compute`
- `harvest` → `collect`
- `klist` → `list`
- `monitor` → `watch`
- `ptt` → `inject`
- `purge` → `clear`
- `renew` → `refresh`
- `s4u` → `delegate`
- `silver` → `forge`
- `tgssub` → `replace`
- `tgtdeleg` → `obtain`
- `triage` → `survey`

### 2. Obfuscate All Console Output
Search for and obfuscate all `Console.WriteLine` statements containing:
- Error messages with "[X]", "[!]", "[+]", "[*]" prefixes
- Kerberos-specific terminology
- File paths and extensions
- Debug information

### 3. Rename Files and Classes
- Rename .cs files to generic names
- Rename class names to non-descriptive names
- Rename method names to generic equivalents

### 4. Obfuscate Variable Names
- Rename variables with obvious purposes
- Use generic names like `data`, `info`, `result`, `config`

### 5. Additional Anti-Analysis Features
- Add environment checks
- Implement timing delays
- Add basic sandbox detection
- Randomize execution flow

## Usage Instructions

### Building the Obfuscated Version
1. Compile with the new project name: `AuthTool.exe`
2. All strings are automatically deobfuscated at runtime
3. Command names have changed - use new generic names

### Adding New Obfuscated Strings
1. Use `ObfuscationHelper.cs` to generate obfuscated strings
2. Replace hardcoded strings with `StringObfuscator.GetString("base64")`
3. Test deobfuscation works correctly

### Command Mapping
- Old: `Ruben.exe kerberoast /user:target`
- New: `AuthTool.exe enumerate /user:target`

## Security Notes

- XOR key can be changed in `StringObfuscator.cs` (currently 0x42)
- Obfuscation is basic - consider additional layers for production use
- File names and metadata have been changed to avoid signature detection
- Console output is obfuscated to prevent runtime analysis

## Testing

1. Compile the project and verify it builds successfully
2. Test basic functionality with renamed commands
3. Verify obfuscated strings display correctly
4. Check that no obvious "Rubeus" references remain in output
