﻿using System;
using System.Collections.Generic;
using AuthTool.lib.Interop;


namespace AuthTool.Commands
{
    public class SessionInfo : IOperation
    {
        public static string CommandName => "currentid";

        public void Execute(Dictionary<string, string> arguments)
        {
            Console.WriteLine(AuthTool.lib.StringObfuscator.GetString("DQoKWypdIEFjdGlvbjogRGlzcGxheSBjdXJyZW50IGlkZW50aWZpZXINCg=="));

            LUID currentLuid = Helpers.GetCurrentLUID();
            Console.WriteLine("[*] Current LogonID (LUID) : {0} ({1})\r\n", currentLuid, (UInt64)currentLuid);
        }
    }
}
