using System;
using System.Text;

namespace ObfuscationHelper
{
    class Program
    {
        // XOR key for string obfuscation - must match the one in StringObfuscator.cs
        private static readonly byte XorKey = 0x42;

        static void Main(string[] args)
        {
            Console.WriteLine("String Obfuscation Helper");
            Console.WriteLine("========================");
            Console.WriteLine();

            // Common strings that need obfuscation
            string[] stringsToObfuscate = {
                // Error messages
                "[!] Unhandled application exception:",
                "[X] Error",
                "[+] Success",
                "[*] Action:",
                
                // Kerberos-related terms
                "Kerberoasting",
                "AS-REP",
                "TGT",
                "TGS",
                "krbtgt",
                "SPN",
                "LUID",
                
                // File extensions
                ".kirbi",
                "ticket.kirbi",
                
                // Common output messages
                "Building AS-REQ",
                "AS-REQ w/o preauth successful!",
                "Please provide a cracking format.",
                "STUPENDOUS",
                
                // Registry and file paths
                "C:\\Windows\\System32\\cmd.exe",
                "C:\\temp\\",
                
                // Domain-related
                "DOMAIN\\USER",
                "DOMAIN_CONTROLLER",
                
                // Authentication terms
                "password",
                "hash",
                "credential",
                "authentication",
                "logon",
                "session"
            };

            Console.WriteLine("Obfuscated strings for use in code:");
            Console.WriteLine("===================================");
            
            foreach (string str in stringsToObfuscate)
            {
                string obfuscated = ObfuscateXor(str);
                Console.WriteLine($"// Original: {str}");
                Console.WriteLine($"StringObfuscator.GetString(\"{obfuscated}\")");
                Console.WriteLine();
            }

            Console.WriteLine("\nPress any key to continue...");
            Console.ReadKey();
        }

        /// <summary>
        /// Obfuscates a string using XOR encoding
        /// </summary>
        /// <param name="input">The string to obfuscate</param>
        /// <returns>Base64 encoded XOR obfuscated string</returns>
        public static string ObfuscateXor(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            byte[] obfuscatedBytes = new byte[inputBytes.Length];

            for (int i = 0; i < inputBytes.Length; i++)
            {
                obfuscatedBytes[i] = (byte)(inputBytes[i] ^ XorKey);
            }

            return Convert.ToBase64String(obfuscatedBytes);
        }

        /// <summary>
        /// Deobfuscates a XOR encoded string
        /// </summary>
        /// <param name="obfuscatedInput">Base64 encoded XOR obfuscated string</param>
        /// <returns>Original string</returns>
        public static string DeobfuscateXor(string obfuscatedInput)
        {
            if (string.IsNullOrEmpty(obfuscatedInput))
                return string.Empty;

            try
            {
                byte[] obfuscatedBytes = Convert.FromBase64String(obfuscatedInput);
                byte[] deobfuscatedBytes = new byte[obfuscatedBytes.Length];

                for (int i = 0; i < obfuscatedBytes.Length; i++)
                {
                    deobfuscatedBytes[i] = (byte)(obfuscatedBytes[i] ^ XorKey);
                }

                return Encoding.UTF8.GetString(deobfuscatedBytes);
            }
            catch
            {
                return obfuscatedInput; // Return original if deobfuscation fails
            }
        }
    }
}
