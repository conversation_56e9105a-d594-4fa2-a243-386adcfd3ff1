using System;
using System.Text;
namespace ObfuscationHelper
{
    class Program
    {
        private static readonly byte XorKey = 0x42;
        static void Main(string[] args)
        {
            Console.WriteLine("String Obfuscation Helper");
            Console.WriteLine("========================");
            Console.WriteLine();
            string[] stringsToObfuscate = {
                "[!] Unhandled application exception:",
                "[X] Error",
                "[+] Success",
                "[*] Action:",
                "Kerberoasting",
                "AS-REP",
                "TGT",
                "TGS",
                "krbtgt",
                "SPN",
                "LUID",
                ".kirbi",
                "ticket.kirbi",
                "Building AS-REQ",
                "AS-REQ w/o preauth successful!",
                "Please provide a cracking format.",
                "STUPENDOUS",
                "C:\\Windows\\System32\\cmd.exe",
                "C:\\temp\\",
                "DOMAIN\\USER",
                "DOMAIN_CONTROLLER",
                "password",
                "hash",
                "credential",
                "authentication",
                "logon",
                "session"
            };
            Console.WriteLine("Obfuscated strings for use in code:");
            Console.WriteLine("===================================");
            foreach (string str in stringsToObfuscate)
            {
                string obfuscated = ObfuscateXor(str);
                Console.WriteLine($"// Original: {str}");
                Console.WriteLine($"StringObfuscator.GetString(\"{obfuscated}\")");
                Console.WriteLine();
            }
            Console.WriteLine("\nPress any key to continue...");
            Console.ReadKey();
        }
        public static string ObfuscateXor(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;
            byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            byte[] obfuscatedBytes = new byte[inputBytes.Length];
            for (int i = 0; i < inputBytes.Length; i++)
            {
                obfuscatedBytes[i] = (byte)(inputBytes[i] ^ XorKey);
            }
            return Convert.ToBase64String(obfuscatedBytes);
        }
        public static string DeobfuscateXor(string obfuscatedInput)
        {
            if (string.IsNullOrEmpty(obfuscatedInput))
                return string.Empty;
            try
            {
                byte[] obfuscatedBytes = Convert.FromBase64String(obfuscatedInput);
                byte[] deobfuscatedBytes = new byte[obfuscatedBytes.Length];
                for (int i = 0; i < obfuscatedBytes.Length; i++)
                {
                    deobfuscatedBytes[i] = (byte)(obfuscatedBytes[i] ^ XorKey);
                }
                return Encoding.UTF8.GetString(deobfuscatedBytes);
            }
            catch
            {
                return obfuscatedInput;
            }
        }
    }
}