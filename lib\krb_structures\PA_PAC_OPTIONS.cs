﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Asn1;
namespace AuthTool
{
    public class PA_PAC_OPTIONS
    {
        public byte[] authprotoFlags { get; set; }
        public PA_PAC_OPTIONS(bool claims, bool branch, bool fullDC, bool rbcd)
        {
            authprotoFlags = new byte[4] { 0, 0, 0, 0 };
            if (claims) authprotoFlags[0] = (byte)(authprotoFlags[0] | 8);
            if (branch) authprotoFlags[0] = (byte)(authprotoFlags[0] | 4);
            if (fullDC) authprotoFlags[0] = (byte)(authprotoFlags[0] | 2);
            if (rbcd) authprotoFlags[0] = (byte)(authprotoFlags[0] | 1);
            authprotoFlags[0] = (byte)(authprotoFlags[0] * 0x10);
        }
        public AsnElt Encode()
        {
            List<AsnElt> allNodes = new List<AsnElt>();
            AsnElt authprotoFlagsAsn = AsnElt.MakeBitString(authprotoFlags);
            authprotoFlagsAsn = AsnElt.MakeImplicit(AsnElt.UNIVERSAL, AsnElt.BIT_STRING, authprotoFlagsAsn);
            AsnElt parent = AsnElt.MakeExplicit(0, authprotoFlagsAsn);
            allNodes.Add(parent);
            AsnElt seq = AsnElt.Make(AsnElt.SEQUENCE, allNodes.ToArray());
            return seq;
        }
    }
}