﻿using System;
using System.Collections.Generic;
using System.IO;
using AuthTool.lib.Interop;
namespace AuthTool.Commands
{
    public class TokenInject : IOperation
    {
        public static string CommandName => "inject";
        public void Execute(Dictionary<string, string> arguments)
        {
            Console.WriteLine("\r\n[*] Action: Import Token");
            LUID luid = new LUID();
            if (arguments.ContainsKey("/luid"))
            {
                try
                {
                    luid = new LUID(arguments["/luid"]);
                }
                catch
                {
                    Console.WriteLine("[X] Invalid LUID format ({0})\r\n", arguments["/luid"]);
                    return;
                }
            }
            if (arguments.ContainsKey("/token"))
            {
                string auth64 = arguments["/token"];
                if (Helpers.IsBase64String(auth64))
                {
                    byte[] authBytes = Convert.FromBase64String(auth64);
                    LSA.ImportToken(authBytes, luid);
                }
                else if (File.Exists(auth64))
                {
                    byte[] authBytes = File.ReadAllBytes(auth64);
                    LSA.ImportToken(authBytes, luid);
                }
                else
                {
                    Console.WriteLine("\r\n[X]/token:X must either be a .auth file or a base64 encoded .auth\r\n");
                }
                return;
            }
            else
            {
                Console.WriteLine("\r\n[X] A /token:X needs to be supplied!\r\n");
                return;
            }
        }
    }
}