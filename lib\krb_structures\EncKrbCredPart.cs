﻿using System;
using Asn1;
using System.Collections.Generic;
namespace AuthTool
{
    public class EncKrbCredPart
    {
        public EncKrbCredPart()
        {
            token_info = new List<KrbCredInfo>();
        }
        public EncKrbCredPart(AsnElt body)
        {
            token_info = new List<KrbCredInfo>();
            byte[] octetString = body.Sub[1].Sub[0].GetOctetString();
            AsnElt body2 = AsnElt.Decode(octetString, false);
            KrbCredInfo info = new KrbCredInfo(body2.Sub[0].Sub[0].Sub[0].Sub[0]);
            token_info.Add(info);
        }
        public AsnElt Encode()
        {
            AsnElt infoAsn = token_info[0].Encode();
            AsnElt seq1 = AsnElt.Make(AsnElt.SEQUENCE, new[] { infoAsn });
            AsnElt seq2 = AsnElt.Make(AsnElt.SEQUENCE, new[] { seq1 });
            seq2 = AsnElt.MakeImplicit(AsnElt.CONTEXT, 0, seq2);
            AsnElt totalSeq = AsnElt.Make(AsnElt.SEQUENCE, new[] { seq2 });
            AsnElt totalSeq2 = AsnElt.Make(AsnElt.SEQUENCE, new[] { totalSeq });
            totalSeq2 = AsnElt.MakeImplicit(AsnElt.APPLICATION, 29, totalSeq2);
            return totalSeq2;
        }
        public List<KrbCredInfo> token_info { get; set; }
    }
}